/* pages/asset/search/search.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.scan-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 建议区域 */
.suggestions-section {
  background: white;
  margin-bottom: 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

/* 搜索历史 */
.history-section {
  padding-bottom: 24rpx;
}

.history-tags {
  padding: 0 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-tag {
  margin: 0 !important;
}

/* 快速搜索 */
.quick-search-section {
  border-top: 1rpx solid #ebedf0;
  padding-top: 16rpx;
}

/* 搜索结果区域 */
.results-section {
  flex: 1;
}

.results-header {
  background: white;
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-count {
  font-size: 28rpx;
  color: #646566;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* 搜索结果列表 */
.search-results {
  padding: 0 16rpx;
}

.result-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.result-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.result-image {
  flex-shrink: 0;
}

.result-info {
  flex: 1;
  min-width: 0;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.label {
  color: #969799;
  min-width: 80rpx;
  margin-right: 16rpx;
}

.value {
  color: #646566;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-arrow {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 查看更多 */
.view-more {
  padding: 32rpx;
}

/* 默认状态 */
.default-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.default-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .result-content {
    padding: 20rpx;
    gap: 20rpx;
  }
  
  .result-name {
    font-size: 30rpx;
  }
  
  .detail-row {
    font-size: 24rpx;
  }
  
  .section-header {
    padding: 20rpx 24rpx 12rpx;
  }
  
  .history-tags {
    padding: 0 24rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .search-section,
  .suggestions-section,
  .results-header,
  .result-item {
    background-color: #2a2a2a !important;
  }
  
  .section-title {
    color: #ffffff;
  }
  
  .results-count {
    color: #cccccc;
  }
  
  .result-name {
    color: #ffffff;
  }
  
  .label {
    color: #999999;
  }
  
  .value {
    color: #cccccc;
  }
}
