// pages/maintenance/index.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../utils/permission.js'

Page({
  data: {
    // 子菜单选项
    menuItems: [
      {
        id: 'task-list',
        icon: 'todo-list-o',
        title: '维护任务',
        subtitle: '查看和执行维护任务',
        color: '#1989fa',
        page: '/pages/maintenance/task/list'
      },
      {
        id: 'asset-scan',
        icon: 'scan',
        title: '资产扫码',
        subtitle: '扫描资产二维码查看维护任务',
        color: '#07c160',
        page: '/pages/maintenance/asset/scan'
      }
    ]
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('维护模块主页面初始化')
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      console.log('刷新维护模块数据')
      // 这里可以添加数据刷新逻辑
    } catch (error) {
      console.error('刷新数据失败:', error)
    }
  },

  /**
   * 菜单项点击事件
   */
  onMenuItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击菜单项:', item)

    if (!item || !item.page) {
      Toast.fail('页面路径配置错误')
      return
    }

    // 跳转到对应页面
    wx.navigateTo({
      url: item.page,
      fail: (error) => {
        console.error('页面跳转失败:', error)
        Toast.fail('页面跳转失败')
      }
    })
  }
})
