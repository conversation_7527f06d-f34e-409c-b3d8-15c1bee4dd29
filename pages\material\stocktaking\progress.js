// pages/material/stocktaking/progress.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { StocktakingAPI } from '../../../utils/stocktaking-api.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务信息
    taskInfo: {},
    
    // 当前标签页
    activeTab: 'personal',
    
    // 个人统计
    personalStats: {},
    
    // 个人记录
    personalRecords: [],
    
    // 团队排行榜
    teamRanking: [],
    
    // 部门统计
    deptStats: [],
    
    // 分类统计
    categoryStats: []
  },

  onLoad(options) {
    console.log('📊 盘点进度页面加载', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId } = options
    if (!taskId) {
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ taskId })

    // 加载数据
    this.loadAllData()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadAllData()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadAllData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载所有数据
   */
  async loadAllData() {
    try {
      await Promise.all([
        this.loadTaskInfo(),
        this.loadPersonalData(),
        this.loadTeamData(),
        this.loadCategoryData()
      ])
    } catch (error) {
      console.error('❌ 加载数据失败:', error)
      Toast.fail('加载数据失败')
    }
  },

  /**
   * 加载任务信息
   */
  async loadTaskInfo() {
    try {
      // 调用真实API - 获取任务进度
      const response = await StocktakingAPI.getTaskProgress(this.data.taskId)
      
      if (response && response.code === 200) {
        // 适配后端返回格式
        const taskInfo = response.data || response.rows?.[0] || {}
        this.setData({
          taskInfo: taskInfo
        })
      } else {
        throw new Error(response?.msg || '获取任务信息失败')
      }
    } catch (error) {
      console.error('❌ 加载任务信息失败:', error)
      throw error
    }
  },



  /**
   * 加载个人数据
   */
  async loadPersonalData() {
    try {
      // 加载个人统计
      const statsResponse = await StocktakingAPI.getMyStatistics({ taskId: this.data.taskId })
      if (statsResponse && statsResponse.code === 200) {
        this.setData({
          personalStats: statsResponse.data || {}
        })
      }

      // 加载个人记录
      const recordsResponse = await StocktakingAPI.getMyHistory({ taskId: this.data.taskId, pageSize: 10 })
      if (recordsResponse && recordsResponse.code === 200) {
        this.setData({
          personalRecords: recordsResponse.rows || recordsResponse.data || []
        })
      }
    } catch (error) {
      console.error('❌ 加载个人数据失败:', error)
      throw error
    }
  },



  /**
   * 加载团队数据
   */
  async loadTeamData() {
    try {
      // 加载团队统计
      const teamResponse = await StocktakingAPI.getTeamStatistics({ taskId: this.data.taskId })
      if (teamResponse && teamResponse.code === 200) {
        const teamData = teamResponse.data || {}
        this.setData({
          teamRanking: teamData.ranking || teamResponse.rows || [],
          deptStats: teamData.deptStats || []
        })
      }
    } catch (error) {
      console.error('❌ 加载团队数据失败:', error)
      throw error
    }
  },

  /**
   * 加载分类数据
   */
  async loadCategoryData() {
    try {
      // 加载分类统计 - 暂时使用空数据，等待后端提供相应接口
      this.setData({
        categoryStats: []
      })
    } catch (error) {
      console.error('❌ 加载分类数据失败:', error)
      throw error
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.name
    console.log('切换标签页:', activeTab)

    this.setData({ activeTab })
  }
})
