/* pages/material/stocktaking/progress.wxss */
.page-container {
  height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 总体进度区域 */
.overall-progress {
  background: linear-gradient(135deg, #1989fa 0%, #07c160 100%);
  padding: 32rpx 24rpx;
  color: #fff;
  margin-bottom: 16rpx;
}

.progress-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.progress-subtitle {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 统计数据 */
.progress-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 进度条区域 */
.progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 500;
}

.progress-percent {
  font-size: 32rpx;
  font-weight: 600;
}

/* 标签页区域 */
.tabs-section {
  background-color: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  overflow: hidden;
}

.personal-tab,
.team-tab,
.category-tab {
  height: 100%;
}

.scroll-container {
  height: 100%;
}

/* 通用区域样式 */
.today-stats,
.history-records,
.team-ranking,
.dept-stats,
.category-stats {
  background-color: #fff;
  margin: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f2f3f5;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

/* 记录列表 */
.record-list {
  padding: 16rpx 24rpx 24rpx;
}

.record-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.asset-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.record-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.asset-code {
  font-size: 24rpx;
  color: #646566;
}

.record-time {
  font-size: 24rpx;
  color: #969799;
}

/* 排行榜列表 */
.ranking-list {
  padding: 16rpx 24rpx 24rpx;
}

.ranking-item {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.ranking-item:last-child {
  margin-bottom: 0;
}

.ranking-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ranking-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.ranking-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #c8c9cc;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.ranking-number.top-three {
  background: linear-gradient(135deg, #ff976a 0%, #ff6b35 100%);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.user-dept {
  font-size: 24rpx;
  color: #646566;
}

.ranking-right {
  text-align: right;
}

.count-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1989fa;
  display: block;
}

.count-label {
  font-size: 24rpx;
  color: #969799;
}

/* 部门列表 */
.dept-list {
  padding: 16rpx 24rpx 24rpx;
}

.dept-item {
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.dept-item:last-child {
  margin-bottom: 0;
}

.dept-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.dept-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dept-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.dept-progress {
  font-size: 28rpx;
  font-weight: 600;
  color: #1989fa;
}

.dept-progress-bar {
  margin: 8rpx 0;
}

.dept-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dept-detail {
  font-size: 24rpx;
  color: #646566;
}

.dept-people {
  font-size: 24rpx;
  color: #969799;
}

/* 分类列表 */
.category-list {
  padding: 16rpx 24rpx 24rpx;
}

.category-item {
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.category-item:last-child {
  margin-bottom: 0;
}

.category-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.category-progress {
  font-size: 28rpx;
  font-weight: 600;
  color: #1989fa;
}

.category-progress-bar {
  margin: 8rpx 0;
}

.category-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-label {
  font-size: 24rpx;
  color: #646566;
}

.info-value {
  font-size: 24rpx;
  color: #323233;
}

.info-value.difference {
  color: #ee0a24;
}
