<!--pages/maintenance/asset/scan.wxml-->
<view class="scan-page">
  <!-- 扫码区域 -->
  <view class="scan-section">
    <view class="scan-title">扫描资产二维码</view>
    <view class="scan-subtitle">扫描资产二维码查看相关维护任务</view>
    
    <view class="scan-button-container">
      <van-button 
        type="primary" 
        icon="scan" 
        size="large" 
        round 
        block 
        loading="{{ scanning }}"
        bind:click="onScanQRCode"
      >{{ scanning ? '扫描中...' : '点击扫描' }}</van-button>
    </view>
  </view>

  <!-- 扫码结果区域 -->
  <block wx:if="{{ assetId }}">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{ loading }}">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </view>
    
    <!-- 资产信息 -->
    <view class="asset-info-section" wx:elif="{{ assetInfo }}">
      <view class="section-title">资产信息</view>
      
      <view class="asset-card">
        <view class="asset-header">
          <view class="asset-name">{{ assetInfo.assetName }}</view>
          <view class="asset-action" bind:tap="onViewAsset">
            <text class="action-text">查看详情</text>
            <van-icon name="arrow" size="14" />
          </view>
        </view>
        
        <view class="asset-details">
          <view class="detail-item">
            <view class="detail-label">资产编码</view>
            <view class="detail-value">{{ assetInfo.assetCode }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">存放位置</view>
            <view class="detail-value">{{ assetInfo.assetLocation }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">使用状态</view>
            <view class="detail-value">{{ assetInfo.assetStatus }}</view>
          </view>
        </view>
      </view>
      
      <!-- 任务列表 -->
      <view class="task-list-section">
        <view class="section-title">维护任务</view>
        
        <view class="task-list" wx:if="{{ taskList.length > 0 }}">
          <view 
            class="task-item {{ item.overdue ? 'task-item--overdue' : '' }}"
            wx:for="{{ taskList }}"
            wx:key="taskId"
            bind:tap="onViewTask"
            data-task="{{ item }}"
          >
            <view class="task-card">
              <!-- 任务标题和状态 -->
              <view class="task-header">
                <view class="task-title">{{ item.taskTitle }}</view>
                <view class="task-status">
                  <van-tag
                    type="{{ item.statusType || (item.status === 1 ? 'warning' : item.status === 2 ? 'primary' : item.status === 7 ? 'success' : 'default') }}"
                    size="medium"
                  >
                    {{ item.statusName }}
                  </van-tag>
                </view>
              </view>

              <!-- 任务信息 -->
              <view class="task-info">
                <view class="task-time">
                  <van-icon name="clock-o" size="14" />
                  <text>{{ item.formattedScheduledTime || item.scheduledTime }}</text>
                  <view class="overdue-tag" wx:if="{{ item.overdue }}">
                    <van-tag type="danger" size="mini">逾期</van-tag>
                  </view>
                </view>
                <view class="task-priority">
                  <van-tag
                    plain
                    type="{{ item.priorityType || (item.priority === 4 ? 'danger' : item.priority === 3 ? 'warning' : item.priority === 2 ? 'primary' : 'default') }}"
                    size="mini"
                  >
                    {{ item.priorityName }}
                  </van-tag>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 无任务状态 -->
        <view class="empty-tasks" wx:else>
          <van-empty image="search" description="暂无维护任务">
            <text class="empty-tip">该资产当前没有需要执行的维护任务</text>
          </van-empty>
        </view>
      </view>
    </view>
    
    <!-- 无资产信息 -->
    <view class="error-container" wx:else>
      <van-empty image="error" description="未找到资产信息">
        <text class="empty-tip">无法获取该资产的信息，请确认二维码是否正确</text>
      </van-empty>
    </view>
  </block>
  
  <!-- 初始状态 -->
  <view class="initial-container" wx:else>
    <van-empty image="search" description="等待扫描">
      <text class="empty-tip">请点击上方按钮扫描资产二维码</text>
    </van-empty>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
