// pages/maintenance/task/detail.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务详情数据
    taskDetail: null,
    
    // 加载状态
    loading: true,
    
    // 操作按钮状态
    canStart: false,
    canEdit: false,
    canSubmit: false,
    canCancel: false
  },

  onLoad(options) {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId } = options
    if (!taskId) {
      console.error('任务ID为空')
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('任务ID:', taskId)
    this.setData({ taskId })

    // 加载任务详情
    this.loadTaskDetail()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadTaskDetail()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadTaskDetail().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载任务详情
   */
  async loadTaskDetail() {
    try {
      this.setData({ loading: true })

      console.log('加载任务详情:', this.data.taskId)

      // 模拟API调用 - 实际开发时替换为真实API
      const mockDetail = this.generateMockTaskDetail(this.data.taskId)
      
      this.setData({
        taskDetail: mockDetail,
        // 根据任务状态设置操作按钮状态
        canStart: mockDetail.status === 1, // 待执行状态可以开始任务
        canEdit: mockDetail.status === 2 || mockDetail.status === 3, // 执行中或草稿状态可以编辑
        canSubmit: mockDetail.status === 2 || mockDetail.status === 3, // 执行中或草稿状态可以提交
        canCancel: mockDetail.status === 1 || mockDetail.status === 2 || mockDetail.status === 3 // 待执行、执行中或草稿状态可以取消
      })

      console.log('任务详情加载成功')

    } catch (error) {
      console.error('加载任务详情失败:', error)
      Toast.fail('加载任务详情失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 开始执行任务
   */
  onStartTask() {
    Dialog.confirm({
      title: '开始任务',
      message: '确定要开始执行该维护任务吗？',
      confirmButtonText: '开始执行',
      cancelButtonText: '取消'
    }).then(() => {
      // 确认开始任务
      Toast.loading({
        message: '正在开始任务...',
        forbidClick: true,
        duration: 0
      })

      // 模拟API调用
      setTimeout(() => {
        Toast.clear()
        Toast.success('任务已开始')

        // 更新任务状态
        this.setData({
          'taskDetail.status': 2,
          'taskDetail.statusName': '执行中',
          'taskDetail.actualStartTime': this.formatCurrentTime(),
          canStart: false,
          canEdit: true,
          canSubmit: true
        })

        // 跳转到任务执行页面
        wx.navigateTo({
          url: `/pages/maintenance/task/execute?taskId=${this.data.taskId}`,
          fail: (error) => {
            console.error('跳转任务执行页面失败:', error)
            Toast.fail('跳转任务执行页面失败')
          }
        })
      }, 1500)
    }).catch(() => {
      // 取消开始任务
      console.log('取消开始任务')
    })
  },

  /**
   * 继续执行任务
   */
  onContinueTask() {
    // 跳转到任务执行页面
    wx.navigateTo({
      url: `/pages/maintenance/task/execute?taskId=${this.data.taskId}`,
      fail: (error) => {
        console.error('跳转任务执行页面失败:', error)
        Toast.fail('跳转任务执行页面失败')
      }
    })
  },

  /**
   * 取消任务
   */
  onCancelTask() {
    Dialog.confirm({
      title: '取消任务',
      message: '确定要取消该维护任务吗？取消后无法恢复。',
      confirmButtonText: '确定取消',
      confirmButtonColor: '#ee0a24',
      cancelButtonText: '再想想'
    }).then(() => {
      // 确认取消任务
      Toast.loading({
        message: '正在取消任务...',
        forbidClick: true,
        duration: 0
      })

      // 模拟API调用
      setTimeout(() => {
        Toast.clear()
        Toast.success('任务已取消')

        // 更新任务状态
        this.setData({
          'taskDetail.status': 8,
          'taskDetail.statusName': '已取消',
          canStart: false,
          canEdit: false,
          canSubmit: false,
          canCancel: false
        })
      }, 1500)
    }).catch(() => {
      // 取消操作
      console.log('取消操作')
    })
  },

  /**
   * 查看资产详情
   */
  onViewAsset() {
    const assetId = this.data.taskDetail.assetId
    if (!assetId) {
      Toast.fail('资产ID不存在')
      return
    }

    // 跳转到资产详情页
    wx.navigateTo({
      url: `/pages/asset/detail/detail?assetId=${assetId}`,
      fail: (error) => {
        console.error('跳转资产详情页失败:', error)
        Toast.fail('跳转资产详情页失败')
      }
    })
  },

  /**
   * 生成模拟任务详情数据
   */
  generateMockTaskDetail(taskId) {
    // 根据任务ID返回不同的模拟数据
    const mockDetails = {
      'MT20250401001': {
        taskId: 'MT20250401001',
        planId: 'MP20250301001',
        planName: '空调系统季度维护计划',
        taskTitle: '空调系统季度维护',
        assetId: 'AS20230001',
        assetName: '中央空调主机',
        assetCode: 'KT-2023-001',
        assetLocation: '设备楼3层机房',
        maintenanceItems: '1. 检查制冷系统压力\n2. 清洗过滤器\n3. 检查电气控制系统\n4. 测试运行状态',
        scheduledTime: '2025-04-15 09:00:00',
        actualStartTime: null,
        actualEndTime: null,
        responsibleType: 1,
        responsibleTypeName: '个人',
        responsibleId: 103,
        responsibleName: '张工',
        executorId: null,
        executorName: null,
        status: 1,
        statusName: '待执行',
        priority: 3,
        priorityName: '高',
        checkResult: null,
        resultDescription: null,
        problemDescription: null,
        solution: null,
        overdue: false,
        partList: [
          {
            recordId: 'TP20250401001',
            partId: 'PT20240001',
            partName: '空调过滤网',
            specModel: 'KT-FW-001',
            unit: '个',
            plannedQuantity: 2,
            actualQuantity: null,
            currentStock: 10,
            useStatus: 1,
            useStatusName: '计划使用'
          },
          {
            recordId: 'TP20250401002',
            partId: 'PT20240002',
            partName: '制冷剂',
            specModel: 'R410A',
            unit: 'kg',
            plannedQuantity: 1.5,
            actualQuantity: null,
            currentStock: 5,
            useStatus: 1,
            useStatusName: '计划使用'
          }
        ]
      },
      'MT20250401002': {
        taskId: 'MT20250401002',
        planId: 'MP20250302001',
        planName: '电梯月度安全检查计划',
        taskTitle: '电梯月度安全检查',
        assetId: 'AS20220001',
        assetName: '1号电梯',
        assetCode: 'DT-2022-001',
        assetLocation: '主楼电梯间',
        maintenanceItems: '1. 检查电梯运行声音\n2. 检查电梯门开关\n3. 检查紧急制动系统\n4. 检查电梯轿厢内设施',
        scheduledTime: '2025-04-10 14:00:00',
        actualStartTime: '2025-04-10 14:05:00',
        actualEndTime: null,
        responsibleType: 1,
        responsibleTypeName: '个人',
        responsibleId: 104,
        responsibleName: '李工',
        executorId: 104,
        executorName: '李工',
        status: 2,
        statusName: '执行中',
        priority: 4,
        priorityName: '紧急',
        checkResult: null,
        resultDescription: '正在检查电梯运行情况',
        problemDescription: '发现电梯门关闭时有异响',
        solution: '需要调整门机构',
        overdue: true,
        partList: [
          {
            recordId: 'TP20250402001',
            partId: 'PT20240003',
            partName: '电梯门传感器',
            specModel: 'DT-CG-002',
            unit: '个',
            plannedQuantity: 1,
            actualQuantity: null,
            currentStock: 3,
            useStatus: 1,
            useStatusName: '计划使用'
          }
        ]
      }
    }

    // 如果找不到对应ID的数据，返回默认数据
    return mockDetails[taskId] || mockDetails['MT20250401001']
  },

  /**
   * 格式化当前时间
   */
  formatCurrentTime() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
  }
})
