// index.js
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

// 引入 Vant Weapp Toast 组件
import Toast from '@vant/weapp/toast/toast';
// 引入权限检查
import { checkPagePermission } from '../../utils/permission.js'
// 引入状态管理
import { createPageMixin, getUser, getLoginStatus } from '../../utils/store.js'
// 引入环境切换工具
import { addEnvSwitcher, showDebugPanel, testApiConnection } from '../../utils/env-switcher.js'
import { getCurrentConfig } from '../../config/env.js'

Page({
  data: {
    // 用户信息
    userInfo: {
      avatar: defaultAvatarUrl,
      nickName: '用户',
    },
    isLoggedIn: false,

    // 欢迎信息
    welcomeTitle: '欢迎回来',
    welcomeSubtitle: '设备监控系统为您服务',

    // 通知栏
    noticeText: '系统升级完成，新增设备远程控制功能',



    // 功能宫格
    gridItems: [
      {
        id: 'device-monitoring',
        icon: 'tv',
        text: '设备监控',
        iconClass: 'grid-icon--info',
        badge: 'NEW',
        page: '/pages/monitor/device'
      },
      {
        id: 'asset-management',
        icon: 'records',
        text: '资产管理',
        iconClass: 'grid-icon--warning',
        badge: '',
        page: '/pages/asset/index'
      },
      {
        id: 'material-management',
        icon: 'goods-collect',
        text: '物资管理',
        iconClass: 'grid-icon--success',
        badge: '',
        page: '/pages/material/index'
      }
    ],

    // 快速操作
    quickActions: [
      {
        id: 'asset-search',
        icon: 'search',
        text: '资产搜索',
        color: '#ff976a'
      },
      {
        id: 'scan-asset',
        icon: 'scan',
        text: '扫码查资产',
        color: '#07c160'
      },
      {
        id: 'add-device',
        icon: 'plus',
        text: '添加设备',
        color: '#1989fa'
      },
      {
        id: 'scan-qr',
        icon: 'qr',
        text: '扫码绑定',
        color: '#7232dd'
      }
    ],



    // 环境信息（开发用）
    envConfig: {},
    showEnvInfo: false
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 添加环境切换功能（仅开发环境）
    addEnvSwitcher(this)



    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 加载环境配置
    this.loadEnvConfig()

    // 更新登录状态和用户信息
    this.updateUserInfo()

    // 设置欢迎信息
    this.setWelcomeInfo()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 刷新用户信息
      this.updateUserInfo()

      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 加载环境配置信息
   */
  loadEnvConfig() {
    const config = getCurrentConfig()

    this.setData({
      envConfig: config,
      showEnvInfo: config.DEBUG
    })

    if (config.DEBUG) {
      console.log('当前环境配置:', config)
    }
  },

  /**
   * 更新用户信息
   */
  updateUserInfo() {
    const isLoggedIn = getLoginStatus()
    const currentUser = getUser()

    if (isLoggedIn && currentUser) {
      this.setData({
        isLoggedIn: true,
        userInfo: {
          avatar: currentUser.avatar || defaultAvatarUrl,
          nickName: currentUser.nickName || currentUser.userName || '用户'
        }
      })
    } else {
      this.setData({
        isLoggedIn: false
      })
    }
  },

  /**
   * 设置欢迎信息
   */
  setWelcomeInfo() {
    const hour = new Date().getHours()
    let greeting = '您好'

    if (hour < 6) {
      greeting = '夜深了'
    } else if (hour < 12) {
      greeting = '早上好'
    } else if (hour < 18) {
      greeting = '下午好'
    } else {
      greeting = '晚上好'
    }

    const userName = this.data.userInfo.nickName

    this.setData({
      welcomeTitle: `${greeting}，${userName}`,
      welcomeSubtitle: '设备监控系统为您服务'
    })
  },



  /**
   * 通知栏关闭
   */
  onNoticeClose() {
    this.setData({
      noticeText: ''
    })
  },

  /**
   * 宫格项点击
   */
  onGridItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击宫格项:', item)

    if (item.page) {
      // 检查页面是否存在，不存在则显示提示
      wx.navigateTo({
        url: item.page,
        fail: () => {
          Toast.fail(`${item.text}功能开发中`)
        }
      })
    } else {
      Toast.fail(`${item.text}功能开发中`)
    }
  },

  /**
   * 快速操作点击
   */
  onQuickActionClick(event) {
    const action = event.currentTarget.dataset.action
    console.log('点击快速操作:', action)

    switch (action.id) {
      case 'asset-search':
        this.goToAssetSearch()
        break
      case 'scan-asset':
        this.scanAsset()
        break
      case 'add-device':
        this.addDevice()
        break
      case 'scan-qr':
        this.scanQRCode()
        break
      default:
        Toast.fail(`${action.text}功能开发中`)
    }
  },



  /**
   * 跳转到个人中心
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile',
      fail: () => {
        Toast.fail('个人中心页面开发中')
      }
    })
  },

  /**
   * 跳转到资产搜索
   */
  goToAssetSearch() {
    wx.navigateTo({
      url: '/pages/asset/search/search',
      fail: () => {
        Toast.fail('资产搜索功能开发中')
      }
    })
  },

  /**
   * 扫码查资产
   */
  scanAsset() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        const scanContent = res.result

        // 跳转到资产搜索页面并传递扫码结果
        wx.navigateTo({
          url: `/pages/asset/search/search?keyword=${encodeURIComponent(scanContent)}`,
          fail: () => {
            Toast.fail('资产搜索功能开发中')
          }
        })
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        Toast.fail('扫码失败')
      }
    })
  },



  /**
   * 添加设备
   */
  addDevice() {
    wx.showModal({
      title: '添加设备',
      content: '请选择添加设备的方式',
      confirmText: '手动添加',
      cancelText: '扫码添加',
      success: (res) => {
        if (res.confirm) {
          Toast.fail('手动添加设备功能开发中')
        } else {
          this.scanQRCode()
        }
      }
    })
  },

  /**
   * 扫码绑定
   */
  scanQRCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        Toast.success('扫码成功，设备绑定功能开发中')
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        Toast.fail('扫码失败')
      }
    })
  },

  /**
   * 跳转到登录页
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  /**
   * 显示调试面板（长按标题触发）
   */
  onTitleLongPress() {
    const config = getCurrentConfig()
    if (config.DEBUG) {
      showDebugPanel()
    }
  },

  /**
   * 测试API连接
   */
  testConnection() {
    testApiConnection()
  },

  /**
   * 切换环境（开发时使用）
   */
  onEnvSwitch() {
    if (this.switchEnv) {
      this.switchEnv()
    }
  },
})
