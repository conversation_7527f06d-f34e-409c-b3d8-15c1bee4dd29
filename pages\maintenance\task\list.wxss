/* pages/maintenance/task/list.wxss */
.task-list-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 32rpx;
}

/* 搜索区域 */
.search-section {
  position: sticky;
  top: 0;
  z-index: 100;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.filter-btn {
  padding: 0 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 筛选标签区域 */
.filter-tags {
  padding: 16rpx 24rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag-item {
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.tag-clear {
  font-size: 24rpx;
  color: #1989fa;
  padding: 4rpx 8rpx;
}

/* 任务列表区域 */
.task-list-container {
  padding: 16rpx 24rpx;
}

.task-list {
  margin-bottom: 24rpx;
}

.task-item {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.task-item:active {
  opacity: 0.8;
}

.task-item--overdue {
  border: 2rpx solid #ee0a24;
}

.task-card {
  padding: 24rpx;
}

/* 任务标题和状态 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.task-status {
  flex-shrink: 0;
}

/* 资产信息 */
.task-asset {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.asset-name {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #646566;
}

.asset-name van-icon {
  margin-right: 8rpx;
}

.asset-code {
  font-size: 24rpx;
  color: #969799;
}

/* 任务信息 */
.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #969799;
}

.task-time van-icon {
  margin-right: 8rpx;
}

.overdue-tag {
  margin-left: 8rpx;
}

.task-priority {
  flex-shrink: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 32rpx 0;
}

/* 空状态 */
.empty-container {
  padding: 64rpx 0;
}

.empty-tips {
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}

/* 筛选弹窗 */
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.filter-close {
  padding: 8rpx;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.checkbox-list {
  display: flex;
  flex-wrap: wrap;
}

.checkbox-item {
  width: 33.33%;
  margin-bottom: 16rpx;
}

.filter-footer {
  padding: 24rpx;
  border-top: 1rpx solid #ebedf0;
}
