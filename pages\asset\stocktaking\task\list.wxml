<!--pages/material/stocktaking/task/list.wxml-->
<view class="page-container">
  
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索任务名称或编号"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>

  <!-- 状态筛选标签页 -->
  <view class="tabs-section">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
      <van-tab title="全部" name="all"></van-tab>
      <van-tab title="待执行" name="pending"></van-tab>
      <van-tab title="执行中" name="running"></van-tab>
      <van-tab title="已完成" name="completed"></van-tab>
    </van-tabs>
  </view>

  <!-- 任务列表 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      refresher-triggered="{{ refreshing }}"
      bind:refresherrefresh="onRefresh"
      bind:scrolltolower="onLoadMore"
    >
      <!-- 任务列表 -->
      <view class="task-list">
        <block wx:if="{{ taskList.length > 0 }}">
          <van-cell-group wx:for="{{ taskList }}" wx:key="taskId" custom-class="task-item-group">
            <van-cell
              bind:click="onTaskItemClick"
              data-task="{{ item }}"
              custom-class="task-item"
              use-slot
            >
              <view class="task-content">
                <!-- 任务头部 -->
                <view class="task-header">
                  <view class="task-title">
                    <text class="task-name">{{ item.taskName }}</text>
                    <van-tag 
                      type="{{ statusMap[item.status].type }}" 
                      size="medium"
                      color="{{ statusMap[item.status].color }}"
                    >
                      {{ statusMap[item.status].label }}
                    </van-tag>
                  </view>
                  <view class="task-code">任务编号：{{ item.taskCode }}</view>
                </view>

                <!-- 任务信息 -->
                <view class="task-info">
                  <view class="info-row">
                    <van-icon name="records" size="16" color="#969799" />
                    <text class="info-text">盘点计划：{{ item.planName }}</text>
                  </view>
                  <view class="info-row">
                    <van-icon name="location-o" size="16" color="#969799" />
                    <text class="info-text">盘点范围：{{ item.scopeDescription }}</text>
                  </view>
                  <view class="info-row">
                    <van-icon name="clock-o" size="16" color="#969799" />
                    <text class="info-text">截止时间：{{ item.deadlineTime }}</text>
                  </view>
                </view>

                <!-- 进度信息 -->
                <view class="progress-section">
                  <view class="progress-header">
                    <text class="progress-text">完成进度：{{ item.completedCount }}/{{ item.totalCount }} ({{ item.progressPercent }}%)</text>
                  </view>
                  <van-progress 
                    percentage="{{ item.progressPercent }}" 
                    stroke-width="6" 
                    color="{{ item.progressPercent === 100 ? '#07c160' : '#1989fa' }}"
                    track-color="#f2f3f5"
                  />
                </view>

                <!-- 操作按钮 -->
                <view class="action-section">
                  <van-button 
                    size="small" 
                    type="default" 
                    bind:click="onViewTaskDetail"
                    data-task="{{ item }}"
                    custom-class="action-btn"
                  >
                    查看详情
                  </van-button>
                  
                  <van-button 
                    wx:if="{{ item.status === 'pending' }}"
                    size="small" 
                    type="primary" 
                    bind:click="onStartTask"
                    data-task="{{ item }}"
                    custom-class="action-btn"
                  >
                    开始盘点
                  </van-button>
                  
                  <van-button 
                    wx:if="{{ item.status === 'running' }}"
                    size="small" 
                    type="primary" 
                    bind:click="onContinueTask"
                    data-task="{{ item }}"
                    custom-class="action-btn"
                  >
                    继续盘点
                  </van-button>
                  
                  <van-button 
                    wx:if="{{ item.status === 'completed' }}"
                    size="small" 
                    type="success" 
                    bind:click="onViewResult"
                    data-task="{{ item }}"
                    custom-class="action-btn"
                  >
                    查看结果
                  </van-button>
                </view>
              </view>
            </van-cell>
          </van-cell-group>
        </block>

        <!-- 空状态 -->
        <van-empty 
          wx:if="{{ !loading && taskList.length === 0 }}"
          image="search" 
          description="{{ searchKeyword ? '未找到相关任务' : '暂无盘点任务' }}"
        />

        <!-- 加载状态 -->
        <view wx:if="{{ loading && taskList.length === 0 }}" class="loading-container">
          <van-loading size="24px">加载中...</van-loading>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{ loading && taskList.length > 0 }}" class="load-more">
          <van-loading size="16px">加载更多...</van-loading>
        </view>

        <!-- 没有更多数据 -->
        <view wx:if="{{ finished && taskList.length > 0 }}" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
