/* pages/material/stocktaking/scan.wxss */
.page-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 24rpx;
}

/* 扫码区域 */
.scan-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.scan-header {
  margin-bottom: 40rpx;
}

.scan-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #323233;
  display: block;
  margin-bottom: 16rpx;
}

.scan-subtitle {
  font-size: 28rpx;
  color: #646566;
  line-height: 1.5;
}

/* 扫码按钮 */
.scan-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 40rpx;
}

.scan-btn {
  height: 96rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
}

.manual-btn {
  height: 80rpx !important;
  font-size: 28rpx !important;
}

/* 扫码提示 */
.scan-tips {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #646566;
  line-height: 1.4;
}

/* 任务进度区域 */
.progress-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

/* 最近记录区域 */
.recent-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: background-color 0.2s;
}

.recent-item:active {
  background-color: #f2f3f5;
}

.recent-content {
  flex: 1;
}

.recent-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.asset-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.recent-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.asset-code {
  font-size: 24rpx;
  color: #646566;
}

.scan-time {
  font-size: 24rpx;
  color: #969799;
}

/* 空状态 */
.empty-recent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #c8c9cc;
  margin-top: 16rpx;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}

/* 手动输入弹窗 */
.manual-dialog {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.dialog-header {
  padding: 32rpx 32rpx 24rpx;
  text-align: center;
  border-bottom: 1rpx solid #ebedf0;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.dialog-content {
  padding: 32rpx;
}

.dialog-actions {
  display: flex;
  border-top: 1rpx solid #ebedf0;
}

.dialog-actions .van-button {
  flex: 1;
  border-radius: 0 !important;
  border: none !important;
}

.dialog-actions .van-button:first-child {
  border-right: 1rpx solid #ebedf0 !important;
}

/* 加载弹窗 */
.loading-dialog {
  padding: 40rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  color: #fff;
}
