/* pages/asset/index.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header-section {
  background: linear-gradient(135deg, #1989fa 0%, #1989fa 100%);
  color: white;
  padding: 60rpx 32rpx 40rpx;
  text-align: center;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 菜单区域 */
.menu-section {
  margin: 32rpx 16rpx;
}

.menu-cell {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .header-section {
    padding: 50rpx 24rpx 30rpx;
  }
  
  .header-title {
    font-size: 44rpx;
  }
  
  .header-subtitle {
    font-size: 26rpx;
  }
  
  .menu-section {
    margin: 24rpx 12rpx;
  }
  
  .menu-icon {
    width: 72rpx;
    height: 72rpx;
    margin-right: 20rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .header-section {
    background: linear-gradient(135deg, #1989fa 0%, #1989fa 100%);
  }
  
  .menu-cell {
    background-color: #2a2a2a !important;
  }
}
