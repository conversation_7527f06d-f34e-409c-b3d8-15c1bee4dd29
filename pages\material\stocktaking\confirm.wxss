/* pages/material/stocktaking/confirm.wxss */
.page-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;
}

/* 通用区域样式 */
.asset-info-section,
.comparison-section,
.found-section,
.difference-section,
.record-section {
  background-color: #fff;
  margin: 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f2f3f5;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

/* 对比区域 */
.comparison-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f2f3f5;
}

.comparison-item:last-child {
  border-bottom: none;
}

.comparison-header {
  margin-bottom: 16rpx;
}

.comparison-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.comparison-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.comparison-row {
  display: flex;
  align-items: center;
}

.comparison-label {
  font-size: 26rpx;
  color: #646566;
  min-width: 160rpx;
}

.comparison-value {
  flex: 1;
  font-size: 26rpx;
}

.comparison-value.book {
  color: #323233;
}

.comparison-value.actual {
  color: #1989fa;
}

.status-selector {
  color: #1989fa;
  text-decoration: underline;
  cursor: pointer;
}

/* 发现状态区域 */
.found-section {
  padding: 24rpx;
}

.found-radio {
  margin-right: 32rpx !important;
}

/* 差异处理区域 */
.difference-section {
  border: 2rpx solid #fee;
  background-color: #fffbf8;
}

/* 现场记录区域 */
.record-section {
  padding: 24rpx;
}

.record-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
  margin-bottom: 16rpx;
}

/* 拍照记录 */
.photo-record {
  margin-bottom: 32rpx;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
}

.photo-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ee0a24;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-add {
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed #c8c9cc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #c8c9cc;
}

/* 语音记录 */
.voice-record {
  margin-bottom: 32rpx;
}

.voice-actions {
  display: flex;
  gap: 16rpx;
}

/* 文本记录 */
.text-record {
  margin-bottom: 0;
}

/* 操作按钮 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx;
  border-top: 1rpx solid #ebedf0;
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 88rpx !important;
  font-size: 32rpx !important;
}
