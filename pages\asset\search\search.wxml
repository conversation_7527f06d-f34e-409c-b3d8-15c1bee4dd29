<!--pages/asset/search/search.wxml-->
<view class="page-container">

  <!-- 搜索区域 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索资产名称、编码或位置"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      bind:focus="onSearchFocus"
      bind:blur="onSearchBlur"
      use-action-slot
      autofocus
    >
      <view slot="action" class="search-actions">
        <van-button
          type="primary"
          size="small"
          bind:tap="onScanCode"
          custom-class="scan-btn"
        >
          <van-icon name="scan" size="16" />
          扫码
        </van-button>
      </view>
    </van-search>
  </view>

  <!-- 搜索建议/历史 -->
  <view class="suggestions-section" wx:if="{{ showSuggestions }}">
    
    <!-- 搜索历史 -->
    <view class="history-section" wx:if="{{ searchHistory.length > 0 && !searchKeyword }}">
      <view class="section-header">
        <text class="section-title">搜索历史</text>
        <van-button
          type="default"
          size="mini"
          bind:tap="clearSearchHistory"
        >
          清除
        </van-button>
      </view>
      
      <view class="history-tags">
        <van-tag
          wx:for="{{ searchHistory }}"
          wx:key="index"
          type="default"
          size="medium"
          closeable
          bind:click="onHistoryClick"
          bind:close="onHistoryRemove"
          data-keyword="{{ item }}"
          data-index="{{ index }}"
          custom-class="history-tag"
        >
          {{ item }}
        </van-tag>
      </view>
    </view>

    <!-- 快速搜索 -->
    <view class="quick-search-section" wx:if="{{ !searchKeyword }}">
      <view class="section-header">
        <text class="section-title">快速搜索</text>
      </view>
      
      <van-cell-group>
        <van-cell
          title="正常状态资产"
          icon="passed"
          is-link
          bind:click="quickSearch"
          data-type="status"
          data-value="1"
        />
        <van-cell
          title="维修中资产"
          icon="warning-o"
          is-link
          bind:click="quickSearch"
          data-type="status"
          data-value="2"
        />
        <van-cell
          title="今日新增资产"
          icon="add-o"
          is-link
          bind:click="quickSearch"
          data-type="date"
          data-value="today"
        />
        <van-cell
          title="高价值资产"
          icon="gold-coin-o"
          is-link
          bind:click="quickSearch"
          data-type="price"
          data-value="high"
        />
      </van-cell-group>
    </view>

  </view>

  <!-- 搜索结果 -->
  <view class="results-section" wx:if="{{ showResults }}">
    
    <!-- 搜索结果统计 -->
    <view class="results-header" wx:if="{{ searchResults.length > 0 }}">
      <text class="results-count">找到 {{ totalCount }} 个相关资产</text>
      <van-button
        type="default"
        size="mini"
        bind:tap="viewAllResults"
      >
        查看全部
      </van-button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{ searching }}">
      <van-loading type="spinner" size="24px">搜索中...</van-loading>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results" wx:elif="{{ searchResults.length > 0 }}">
      <van-cell
        wx:for="{{ searchResults }}"
        wx:key="assetId"
        bind:click="onAssetItemClick"
        data-asset="{{ item }}"
        custom-class="result-item"
        use-slot
      >
        <view class="result-content">
          <!-- 资产图片 -->
          <view class="result-image">
            <van-image
              src="{{ item.imageUrl || '/images/default-asset.png' }}"
              width="80rpx"
              height="80rpx"
              radius="8rpx"
              error-icon="photo-fail"
            />
          </view>

          <!-- 资产信息 -->
          <view class="result-info">
            <view class="result-header">
              <text class="result-name">{{ item.assetName }}</text>
              <van-tag
                type="{{ item.statusType }}"
                size="small"
              >
                {{ item.statusName }}
              </van-tag>
            </view>

            <view class="result-details">
              <view class="detail-row">
                <text class="label">编码:</text>
                <text class="value">{{ item.assetCode }}</text>
              </view>
              <view class="detail-row" wx:if="{{ item.location }}">
                <text class="label">位置:</text>
                <text class="value">{{ item.location }}</text>
              </view>
              <view class="detail-row" wx:if="{{ item.assetType }}">
                <text class="label">类型:</text>
                <text class="value">{{ item.assetType }}</text>
              </view>
            </view>
          </view>

          <!-- 箭头图标 -->
          <view class="result-arrow">
            <van-icon name="arrow" size="16" color="#c8c9cc" />
          </view>
        </view>
      </van-cell>

      <!-- 查看更多 -->
      <view class="view-more" wx:if="{{ hasMore }}">
        <van-button
          type="default"
          size="large"
          bind:tap="loadMoreResults"
          loading="{{ loadingMore }}"
        >
          {{ loadingMore ? '加载中...' : '查看更多' }}
        </van-button>
      </view>
    </view>

    <!-- 无搜索结果 -->
    <van-empty
      wx:elif="{{ !searching }}"
      image="search"
      description="未找到相关资产"
    >
      <van-button
        round
        type="primary"
        size="small"
        bind:tap="onScanCode"
      >
        扫码搜索
      </van-button>
    </van-empty>

  </view>

  <!-- 默认状态 -->
  <view class="default-section" wx:if="{{ !showSuggestions && !showResults }}">
    <van-empty
      image="search"
      description="输入关键词搜索资产"
    >
      <view class="default-actions">
        <van-button
          round
          type="primary"
          size="small"
          bind:tap="onScanCode"
          custom-class="action-btn"
        >
          <van-icon name="scan" size="16" />
          扫码搜索
        </van-button>
        <van-button
          round
          type="default"
          size="small"
          bind:tap="goToAssetList"
          custom-class="action-btn"
        >
          <van-icon name="apps-o" size="16" />
          浏览全部
        </van-button>
      </view>
    </van-empty>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
