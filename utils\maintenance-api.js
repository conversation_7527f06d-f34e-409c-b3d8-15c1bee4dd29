/**
 * 维护模块API工具类
 * 封装所有与维护相关的API调用
 */

import { request } from './request.js'

class MaintenanceAPI {
  /**
   * 查询我的任务
   * @param {Object} params 查询参数
   * @param {Array} params.statusList 任务状态列表，如[1,2,3]，不传则查询所有状态
   * @param {number} params.pageNum 页码，默认1
   * @param {number} params.pageSize 每页大小，默认10
   * @returns {Promise}
   */
  static getMyTasks(params = {}) {
    return request({
      url: '/maintenance/task/myTasks',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取任务详情
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static getTaskDetail(taskId) {
    return request({
      url: `/maintenance/task/${taskId}`,
      method: 'GET'
    })
  }

  /**
   * 开始执行任务
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static startTask(taskId) {
    return request({
      url: `/maintenance/task/start/${taskId}`,
      method: 'POST'
    })
  }

  /**
   * 保存草稿
   * @param {Object} taskDto 任务数据对象
   * @returns {Promise}
   */
  static saveDraft(taskDto) {
    return request({
      url: '/maintenance/task/draft',
      method: 'POST',
      data: { taskDto }
    })
  }

  /**
   * 提交任务结果
   * @param {Object} taskDto 任务数据对象
   * @returns {Promise}
   */
  static submitTask(taskDto) {
    return request({
      url: '/maintenance/task/submit',
      method: 'POST',
      data: { taskDto }
    })
  }

  /**
   * 根据资产ID查询任务
   * @param {string} assetId 资产ID
   * @returns {Promise}
   */
  static getTasksByAsset(assetId) {
    return request({
      url: '/maintenance/task/list',
      method: 'GET',
      data: { assetId }
    })
  }

  /**
   * 取消任务
   * @param {string} taskId 任务ID
   * @param {string} reason 取消原因
   * @returns {Promise}
   */
  static cancelTask(taskId, reason = '') {
    return request({
      url: `/maintenance/task/cancel/${taskId}`,
      method: 'POST',
      data: { reason }
    })
  }

  /**
   * 获取任务执行历史
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static getTaskHistory(taskId) {
    return request({
      url: `/maintenance/task/history/${taskId}`,
      method: 'GET'
    })
  }

  /**
   * 上传任务照片
   * @param {string} taskId 任务ID
   * @param {Array} photoList 照片列表
   * @returns {Promise}
   */
  static uploadTaskPhotos(taskId, photoList) {
    return request({
      url: '/maintenance/task/photos/upload',
      method: 'POST',
      data: {
        taskId,
        photoList
      }
    })
  }

  /**
   * 获取任务照片
   * @param {string} taskId 任务ID
   * @returns {Promise}
   */
  static getTaskPhotos(taskId) {
    return request({
      url: `/maintenance/task/photos/${taskId}`,
      method: 'GET'
    })
  }

  /**
   * 删除任务照片
   * @param {string} photoId 照片ID
   * @returns {Promise}
   */
  static deleteTaskPhoto(photoId) {
    return request({
      url: `/maintenance/task/photos/${photoId}`,
      method: 'DELETE'
    })
  }

  /**
   * 获取维护计划列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  static getMaintenancePlans(params = {}) {
    return request({
      url: '/maintenance/plan/list',
      method: 'GET',
      data: params
    })
  }

  /**
   * 获取维护计划详情
   * @param {string} planId 计划ID
   * @returns {Promise}
   */
  static getPlanDetail(planId) {
    return request({
      url: `/maintenance/plan/${planId}`,
      method: 'GET'
    })
  }

  /**
   * 格式化任务状态
   * @param {number} status 状态值
   * @returns {Object} 状态信息
   */
  static formatTaskStatus(status) {
    const statusMap = {
      1: { name: '待执行', type: 'warning', color: '#ff976a' },
      2: { name: '执行中', type: 'primary', color: '#1989fa' },
      3: { name: '草稿', type: 'default', color: '#969799' },
      4: { name: '待审核', type: 'info', color: '#7232dd' },
      5: { name: '审核通过', type: 'success', color: '#07c160' },
      6: { name: '审核不通过', type: 'danger', color: '#ee0a24' },
      7: { name: '已完成', type: 'success', color: '#07c160' },
      8: { name: '已取消', type: 'default', color: '#c8c9cc' }
    }

    return statusMap[status] || { name: '未知', type: 'default', color: '#969799' }
  }

  /**
   * 格式化任务优先级
   * @param {number} priority 优先级值
   * @returns {Object} 优先级信息
   */
  static formatTaskPriority(priority) {
    const priorityMap = {
      1: { name: '低', type: 'default', color: '#969799' },
      2: { name: '中', type: 'primary', color: '#1989fa' },
      3: { name: '高', type: 'warning', color: '#ff976a' },
      4: { name: '紧急', type: 'danger', color: '#ee0a24' }
    }

    return priorityMap[priority] || { name: '未知', type: 'default', color: '#969799' }
  }

  /**
   * 格式化日期时间
   * @param {string} dateTime 日期时间字符串
   * @returns {string} 格式化后的日期时间
   */
  static formatDateTime(dateTime) {
    if (!dateTime) return ''
    
    try {
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hour}:${minute}`
    } catch (error) {
      console.error('日期格式化失败:', error)
      return dateTime
    }
  }

  /**
   * 判断任务是否逾期
   * @param {string} scheduledTime 计划时间
   * @param {number} status 任务状态
   * @returns {boolean} 是否逾期
   */
  static isTaskOverdue(scheduledTime, status) {
    // 已完成或已取消的任务不算逾期
    if (status === 7 || status === 8) {
      return false
    }
    
    if (!scheduledTime) return false
    
    try {
      const scheduled = new Date(scheduledTime)
      const now = new Date()
      return now > scheduled
    } catch (error) {
      console.error('判断逾期失败:', error)
      return false
    }
  }

  /**
   * 格式化任务列表数据
   * @param {Array} taskList 任务列表
   * @returns {Array} 格式化后的任务列表
   */
  static formatTaskList(taskList) {
    if (!Array.isArray(taskList)) {
      return []
    }

    return taskList.map(task => {
      const statusInfo = this.formatTaskStatus(task.status)
      const priorityInfo = this.formatTaskPriority(task.priority)
      
      return {
        ...task,
        statusName: statusInfo.name,
        statusType: statusInfo.type,
        statusColor: statusInfo.color,
        priorityName: priorityInfo.name,
        priorityType: priorityInfo.type,
        priorityColor: priorityInfo.color,
        formattedScheduledTime: this.formatDateTime(task.scheduledTime),
        formattedActualStartTime: this.formatDateTime(task.actualStartTime),
        formattedActualEndTime: this.formatDateTime(task.actualEndTime),
        overdue: this.isTaskOverdue(task.scheduledTime, task.status)
      }
    })
  }

  /**
   * 处理API响应
   * @param {Object} response API响应
   * @returns {Object} 处理后的数据
   */
  static handleApiResponse(response) {
    if (!response) {
      throw new Error('响应数据为空')
    }

    if (response.code !== 200) {
      throw new Error(response.msg || '请求失败')
    }

    return response.data
  }
}

export default MaintenanceAPI
