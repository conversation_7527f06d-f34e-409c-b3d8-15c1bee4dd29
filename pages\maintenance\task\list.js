// pages/maintenance/task/list.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import MaintenanceAPI from '../../../utils/maintenance-api.js'

Page({
  data: {
    // 任务列表数据
    taskList: [],
    
    // 分页信息
    pagination: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hasMore: true
    },
    
    // 搜索关键词
    searchKeyword: '',
    
    // 状态筛选
    statusFilter: [],
    statusOptions: [
      { value: 1, label: '待执行', color: '#ff976a' },
      { value: 2, label: '执行中', color: '#1989fa' },
      { value: 3, label: '草稿', color: '#969799' },
      { value: 4, label: '待审核', color: '#7232dd' },
      { value: 5, label: '审核通过', color: '#07c160' },
      { value: 6, label: '审核不通过', color: '#ee0a24' },
      { value: 7, label: '已完成', color: '#07c160' },
      { value: 8, label: '已取消', color: '#c8c9cc' }
    ],
    
    // 加载状态
    loading: false,
    refreshing: false,
    isEmpty: false,
    
    // 筛选弹窗
    showFilterPopup: false
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshTaskList()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshTaskList().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    // 上拉加载更多
    this.loadMoreTasks()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('维护任务列表页面初始化')
    this.loadTaskList()
  },

  /**
   * 加载任务列表
   */
  async loadTaskList(isRefresh = false) {
    if (this.data.loading) return

    try {
      this.setData({ loading: true })

      if (isRefresh) {
        this.setData({
          'pagination.pageNum': 1,
          'pagination.hasMore': true,
          taskList: []
        })
      }

      // 构建请求参数
      const params = {
        pageNum: this.data.pagination.pageNum,
        pageSize: this.data.pagination.pageSize
      }

      // 添加状态筛选
      if (this.data.statusFilter.length > 0) {
        params.statusList = this.data.statusFilter
      }

      console.log('请求参数:', params)

      // 调用API获取任务列表
      let taskData = []
      try {
        const response = await MaintenanceAPI.getMyTasks(params)
        const data = MaintenanceAPI.handleApiResponse(response)
        taskData = MaintenanceAPI.formatTaskList(data || [])
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        // API调用失败时使用模拟数据
        taskData = this.generateMockTaskList()
      }

      const newTasks = isRefresh ? taskData : [...this.data.taskList, ...taskData]
      
      this.setData({
        taskList: newTasks,
        isEmpty: newTasks.length === 0,
        'pagination.hasMore': taskData.length === this.data.pagination.pageSize
      })

      console.log('任务列表加载成功，共', newTasks.length, '条')

    } catch (error) {
      console.error('加载任务列表失败:', error)
      Toast.fail('加载任务列表失败')
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  /**
   * 刷新任务列表
   */
  async refreshTaskList() {
    this.setData({ refreshing: true })
    await this.loadTaskList(true)
  },

  /**
   * 加载更多任务
   */
  async loadMoreTasks() {
    if (!this.data.pagination.hasMore || this.data.loading) return

    this.setData({
      'pagination.pageNum': this.data.pagination.pageNum + 1
    })

    await this.loadTaskList()
  },

  /**
   * 搜索任务
   */
  onSearchTask(event) {
    const keyword = event.detail || event.detail.value || ''
    console.log('搜索关键词:', keyword)
    
    this.setData({ searchKeyword: keyword })
    
    // 延迟搜索，避免频繁请求
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      this.refreshTaskList()
    }, 500)
  },

  /**
   * 清除搜索
   */
  onClearSearch() {
    this.setData({ searchKeyword: '' })
    this.refreshTaskList()
  },

  /**
   * 显示筛选弹窗
   */
  showFilterPopup() {
    this.setData({ showFilterPopup: true })
  },

  /**
   * 关闭筛选弹窗
   */
  closeFilterPopup() {
    this.setData({ showFilterPopup: false })
  },

  /**
   * 状态筛选变化
   */
  onStatusFilterChange(event) {
    const statusFilter = event.detail
    console.log('状态筛选变化:', statusFilter)

    this.setData({ statusFilter })
    this.refreshTaskList()
  },

  /**
   * 移除状态筛选标签
   */
  onRemoveStatusFilter(event) {
    const index = event.currentTarget.dataset.index
    const statusFilter = [...this.data.statusFilter]
    statusFilter.splice(index, 1)

    this.setData({ statusFilter })
    this.refreshTaskList()
  },

  /**
   * 清除所有状态筛选
   */
  onClearStatusFilter() {
    this.setData({ statusFilter: [] })
    this.refreshTaskList()
  },

  /**
   * 任务项点击事件
   */
  onTaskItemClick(event) {
    const task = event.currentTarget.dataset.task
    console.log('点击任务:', task)

    // 跳转到任务详情页
    wx.navigateTo({
      url: `/pages/maintenance/task/detail?taskId=${task.taskId}`,
      fail: (error) => {
        console.error('跳转任务详情失败:', error)
        Toast.fail('跳转任务详情失败')
      }
    })
  },

  /**
   * 生成模拟任务数据
   */
  generateMockTaskList() {
    const mockTasks = [
      {
        taskId: 'MT20250401001',
        taskTitle: '空调系统季度维护',
        assetName: '中央空调主机',
        assetCode: 'KT-2023-001',
        scheduledTime: '2025-04-15 09:00:00',
        priority: 3,
        priorityName: '高',
        status: 1,
        statusName: '待执行',
        overdue: false
      },
      {
        taskId: 'MT20250401002',
        taskTitle: '电梯月度安全检查',
        assetName: '1号电梯',
        assetCode: 'DT-2022-001',
        scheduledTime: '2025-04-10 14:00:00',
        priority: 4,
        priorityName: '紧急',
        status: 2,
        statusName: '执行中',
        overdue: true
      },
      {
        taskId: 'MT20250401003',
        taskTitle: '消防设备年度检测',
        assetName: '消防泵',
        assetCode: 'XF-2021-003',
        scheduledTime: '2025-04-20 10:00:00',
        priority: 2,
        priorityName: '中',
        status: 1,
        statusName: '待执行',
        overdue: false
      },
      {
        taskId: 'MT20250401004',
        taskTitle: '发电机组保养',
        assetName: '备用发电机',
        assetCode: 'FD-2020-002',
        scheduledTime: '2025-04-25 08:00:00',
        priority: 3,
        priorityName: '高',
        status: 7,
        statusName: '已完成',
        overdue: false
      },
      {
        taskId: 'MT20250401005',
        taskTitle: '照明系统检修',
        assetName: '楼宇照明',
        assetCode: 'ZM-2022-005',
        scheduledTime: '2025-04-18 16:00:00',
        priority: 1,
        priorityName: '低',
        status: 3,
        statusName: '草稿',
        overdue: false
      }
    ]

    return mockTasks
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
