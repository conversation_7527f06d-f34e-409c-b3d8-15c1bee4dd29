<!--pages/material/stocktaking/history.wxml-->
<view class="page-container">
  
  <!-- 搜索和筛选区域 -->
  <view class="search-section">
    <!-- 搜索栏 -->
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索资产名称或编码"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onShowFilter">
        <van-icon name="filter-o" size="20" />
      </view>
    </van-search>

    <!-- 筛选条件显示 -->
    <view wx:if="{{ hasFilter }}" class="filter-tags">
      <view class="filter-tag-list">
        <van-tag 
          wx:if="{{ filterData.startDate }}"
          type="primary" 
          size="medium"
          closeable
          bind:close="onClearDateFilter"
        >
          {{ filterData.startDate }} 至 {{ filterData.endDate }}
        </van-tag>
        
        <van-tag 
          wx:if="{{ filterData.status }}"
          type="primary" 
          size="medium"
          closeable
          bind:close="onClearStatusFilter"
        >
          {{ statusMap[filterData.status] }}
        </van-tag>
        
        <van-tag 
          wx:if="{{ filterData.hasDifference !== '' }}"
          type="primary" 
          size="medium"
          closeable
          bind:close="onClearDifferenceFilter"
        >
          {{ filterData.hasDifference ? '有差异' : '无差异' }}
        </van-tag>
      </view>
      
      <view class="clear-all" bind:tap="onClearAllFilter">
        <text>清空</text>
      </view>
    </view>
  </view>

  <!-- 历史记录列表 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      refresher-triggered="{{ refreshing }}"
      bind:refresherrefresh="onRefresh"
      bind:scrolltolower="onLoadMore"
    >
      <view class="history-list">
        <block wx:if="{{ historyList.length > 0 }}">
          <van-cell
            wx:for="{{ historyList }}"
            wx:key="recordId"
            bind:click="onHistoryItemClick"
            data-item="{{ item }}"
            custom-class="history-item"
            use-slot
          >
            <view class="history-content">
              <!-- 记录头部 -->
              <view class="history-header">
                <view class="asset-info">
                  <text class="asset-name">{{ item.assetName }}</text>
                  <text class="asset-code">{{ item.assetCode }}</text>
                </view>
                <view class="record-tags">
                  <van-tag 
                    type="{{ item.hasDifference ? 'danger' : 'success' }}" 
                    size="small"
                  >
                    {{ item.hasDifference ? '有差异' : '正常' }}
                  </van-tag>
                  
                  <van-tag 
                    wx:if="{{ item.isOffline }}"
                    type="warning" 
                    size="small"
                  >
                    离线
                  </van-tag>
                </view>
              </view>

              <!-- 记录信息 -->
              <view class="history-info">
                <view class="info-row">
                  <van-icon name="location-o" size="16" color="#969799" />
                  <text class="info-text">位置：{{ item.actualLocation }}</text>
                </view>
                <view class="info-row">
                  <van-icon name="label-o" size="16" color="#969799" />
                  <text class="info-text">状态：{{ item.actualStatus }}</text>
                </view>
                <view class="info-row">
                  <van-icon name="contact" size="16" color="#969799" />
                  <text class="info-text">盘点人：{{ item.operatorName }}</text>
                </view>
                <view class="info-row">
                  <van-icon name="clock-o" size="16" color="#969799" />
                  <text class="info-text">时间：{{ item.recordTime }}</text>
                </view>
              </view>

              <!-- 差异信息 -->
              <view wx:if="{{ item.hasDifference }}" class="difference-info">
                <view class="difference-header">
                  <van-icon name="warning-o" size="16" color="#ee0a24" />
                  <text class="difference-title">差异信息</text>
                </view>
                <view class="difference-content">
                  <text class="difference-text">{{ item.differenceReason }}</text>
                </view>
              </view>

              <!-- 现场记录 -->
              <view wx:if="{{ item.hasRecord }}" class="record-info">
                <view class="record-indicators">
                  <view wx:if="{{ item.photoCount > 0 }}" class="indicator-item">
                    <van-icon name="photo-o" size="16" color="#1989fa" />
                    <text class="indicator-text">{{ item.photoCount }}张照片</text>
                  </view>
                  
                  <view wx:if="{{ item.hasVoice }}" class="indicator-item">
                    <van-icon name="volume-o" size="16" color="#1989fa" />
                    <text class="indicator-text">语音备注</text>
                  </view>
                  
                  <view wx:if="{{ item.remark }}" class="indicator-item">
                    <van-icon name="edit" size="16" color="#1989fa" />
                    <text class="indicator-text">文字备注</text>
                  </view>
                </view>
              </view>
            </view>
          </van-cell>
        </block>

        <!-- 空状态 -->
        <van-empty 
          wx:if="{{ !loading && historyList.length === 0 }}"
          image="search" 
          description="{{ searchKeyword || hasFilter ? '未找到相关记录' : '暂无盘点历史' }}"
        />

        <!-- 加载状态 -->
        <view wx:if="{{ loading && historyList.length === 0 }}" class="loading-container">
          <van-loading size="24px">加载中...</van-loading>
        </view>

        <!-- 加载更多 -->
        <view wx:if="{{ loading && historyList.length > 0 }}" class="load-more">
          <van-loading size="16px">加载更多...</van-loading>
        </view>

        <!-- 没有更多数据 -->
        <view wx:if="{{ finished && historyList.length > 0 }}" class="no-more">
          <text>没有更多数据了</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 筛选弹窗 -->
  <van-popup 
    show="{{ showFilterDialog }}" 
    position="bottom" 
    round
    bind:close="onCloseFilter"
  >
    <view class="filter-dialog">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <van-icon name="cross" size="20" bind:click="onCloseFilter" />
      </view>
      
      <view class="filter-content">
        <!-- 时间范围 -->
        <view class="filter-section">
          <view class="filter-label">时间范围</view>
          <view class="date-range">
            <van-field
              value="{{ tempFilterData.startDate }}"
              placeholder="开始日期"
              readonly
              is-link
              bind:click="onSelectStartDate"
            />
            <text class="date-separator">至</text>
            <van-field
              value="{{ tempFilterData.endDate }}"
              placeholder="结束日期"
              readonly
              is-link
              bind:click="onSelectEndDate"
            />
          </view>
        </view>

        <!-- 盘点状态 -->
        <view class="filter-section">
          <view class="filter-label">盘点状态</view>
          <view class="status-options">
            <view 
              wx:for="{{ statusOptions }}" 
              wx:key="value"
              class="status-option {{ tempFilterData.status === item.value ? 'active' : '' }}"
              bind:tap="onSelectStatus"
              data-value="{{ item.value }}"
            >
              <text>{{ item.label }}</text>
            </view>
          </view>
        </view>

        <!-- 差异情况 -->
        <view class="filter-section">
          <view class="filter-label">差异情况</view>
          <view class="difference-options">
            <view 
              class="difference-option {{ tempFilterData.hasDifference === '' ? 'active' : '' }}"
              bind:tap="onSelectDifference"
              data-value=""
            >
              <text>全部</text>
            </view>
            <view 
              class="difference-option {{ tempFilterData.hasDifference === true ? 'active' : '' }}"
              bind:tap="onSelectDifference"
              data-value="{{ true }}"
            >
              <text>有差异</text>
            </view>
            <view 
              class="difference-option {{ tempFilterData.hasDifference === false ? 'active' : '' }}"
              bind:tap="onSelectDifference"
              data-value="{{ false }}"
            >
              <text>无差异</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="filter-actions">
        <van-button 
          type="default" 
          size="large" 
          bind:click="onResetFilter"
        >
          重置
        </van-button>
        <van-button 
          type="primary" 
          size="large" 
          bind:click="onConfirmFilter"
        >
          确定
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 日期选择器 -->
  <van-popup 
    show="{{ showDatePicker }}" 
    position="bottom" 
    round
    bind:close="onCloseDatePicker"
  >
    <van-datetime-picker
      type="date"
      value="{{ currentDate }}"
      bind:confirm="onConfirmDate"
      bind:cancel="onCloseDatePicker"
    />
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
