// utils/data-adapter.js
// 数据适配工具，用于处理后端返回的不同数据格式

/**
 * 适配后端分页数据格式
 * 支持两种格式：
 * 1. RuoYi格式：{total, rows, code, msg}
 * 2. 标准格式：{records, total, size, current, pages, code, msg}
 * 
 * @param {Object} response 后端响应数据
 * @returns {Object} 标准化的分页数据
 */
export function adaptPageData(response) {
  if (!response || response.code !== 200) {
    return {
      records: [],
      total: 0,
      size: 10,
      current: 1,
      pages: 0,
      success: false,
      message: response?.msg || '请求失败'
    }
  }

  // RuoYi格式：{total, rows, code, msg}
  if (response.rows !== undefined) {
    const total = response.total || 0
    const size = response.size || 10
    const pages = total > 0 ? Math.ceil(total / size) : 1

    return {
      records: response.rows || [],
      total: total,
      size: size,
      current: response.current || 1,
      pages: pages,
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 标准格式：{records, total, size, current, pages}
  if (response.data) {
    const data = response.data
    return {
      records: data.records || data.rows || [],
      total: data.total || 0,
      size: data.size || 10,
      current: data.current || 1,
      pages: data.pages || Math.ceil((data.total || 0) / (data.size || 10)),
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 直接返回数组的情况
  if (Array.isArray(response.data)) {
    return {
      records: response.data,
      total: response.data.length,
      size: response.data.length,
      current: 1,
      pages: 1,
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 其他情况
  return {
    records: [],
    total: 0,
    size: 10,
    current: 1,
    pages: 0,
    success: false,
    message: response?.msg || '数据格式错误'
  }
}

/**
 * 适配后端单条数据格式
 * 支持多种格式：
 * 1. {data: object, code, msg}
 * 2. {rows: [object], code, msg}
 * 3. 直接返回对象
 * 
 * @param {Object} response 后端响应数据
 * @returns {Object} 标准化的单条数据
 */
export function adaptSingleData(response) {
  if (!response || response.code !== 200) {
    return {
      data: null,
      success: false,
      message: response?.msg || '请求失败'
    }
  }

  // 有data字段的情况
  if (response.data !== undefined) {
    return {
      data: response.data,
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 有rows字段且是数组的情况，取第一个
  if (Array.isArray(response.rows) && response.rows.length > 0) {
    return {
      data: response.rows[0],
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 有rows字段但为空数组的情况
  if (Array.isArray(response.rows) && response.rows.length === 0) {
    return {
      data: null,
      success: false,
      message: response.msg || '未找到数据'
    }
  }

  // 其他情况
  return {
    data: null,
    success: false,
    message: response?.msg || '数据格式错误'
  }
}

/**
 * 适配后端列表数据格式
 * 支持多种格式：
 * 1. {data: array, code, msg}
 * 2. {rows: array, code, msg}
 * 3. 直接返回数组
 * 
 * @param {Object} response 后端响应数据
 * @returns {Object} 标准化的列表数据
 */
export function adaptListData(response) {
  if (!response || response.code !== 200) {
    return {
      list: [],
      success: false,
      message: response?.msg || '请求失败'
    }
  }

  // 有data字段且是数组的情况
  if (Array.isArray(response.data)) {
    return {
      list: response.data,
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // 有rows字段且是数组的情况
  if (Array.isArray(response.rows)) {
    return {
      list: response.rows,
      success: true,
      message: response.msg || '操作成功'
    }
  }

  // data是对象但可能包含列表的情况
  if (response.data && typeof response.data === 'object') {
    // 尝试从data对象中提取数组
    const possibleArrays = Object.values(response.data).filter(Array.isArray)
    if (possibleArrays.length > 0) {
      return {
        list: possibleArrays[0],
        success: true,
        message: response.msg || '操作成功'
      }
    }
  }

  // 其他情况
  return {
    list: [],
    success: false,
    message: response?.msg || '数据格式错误'
  }
}

/**
 * 检查响应是否成功
 * @param {Object} response 后端响应数据
 * @returns {boolean} 是否成功
 */
export function isResponseSuccess(response) {
  return response && response.code === 200
}

/**
 * 获取响应消息
 * @param {Object} response 后端响应数据
 * @returns {string} 响应消息
 */
export function getResponseMessage(response) {
  return response?.msg || '操作失败'
}
