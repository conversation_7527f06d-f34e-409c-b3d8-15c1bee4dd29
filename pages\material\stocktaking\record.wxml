<!--pages/material/stocktaking/record.wxml-->
<view class="page-container">

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />

  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />

  <!-- 扫码盘点区域 -->
  <view class="scan-section">
    <van-grid column-num="2" border="{{false}}" gutter="16">
      <van-grid-item use-slot bind:click="onScanCode">
        <view class="scan-item">
          <view class="scan-icon">
            <van-icon name="scan" size="32" color="#1989fa" />
          </view>
          <text class="scan-text">扫码盘点</text>
        </view>
      </van-grid-item>

      <van-grid-item use-slot bind:click="onManualInput">
        <view class="scan-item">
          <view class="scan-icon">
            <van-icon name="edit" size="32" color="#07c160" />
          </view>
          <text class="scan-text">手动输入</text>
        </view>
      </van-grid-item>
    </van-grid>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索物品名称或编码"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>

  <!-- 盘点明细列表 -->
  <view class="list-section">
    <scroll-view
      class="scroll-container"
      scroll-y="{{true}}"
      refresher-enabled="{{true}}"
      refresher-triggered="{{refreshing}}"
      bindrefresherrefresh="onPullDownRefresh"
      bindscrolltolower="onLoadMore"
    >
      <view wx:if="{{ !loading && detailList.length > 0 }}">
        <block wx:for="{{ detailList }}" wx:key="detailId">
          <van-cell
            custom-class="detail-item"
            bind:click="onDetailItemClick"
            data-detail="{{ item }}"
          >
            <view slot="title" class="item-content">
              <!-- 物品图片 -->
              <view class="item-image">
                <van-image
                  src="{{ item.imageUrl || '/images/default-item.png' }}"
                  width="80rpx"
                  height="80rpx"
                  radius="8rpx"
                  error-icon="photo-fail"
                />
              </view>

              <!-- 物品信息 -->
              <view class="item-info">
                <view class="item-header">
                  <text class="item-name">{{ item.itemName }}</text>
                  <van-tag
                    type="{{ item.status === 1 ? 'success' : item.status === 2 ? 'warning' : 'default' }}"
                    size="mini"
                  >
                    {{ item.status === 1 ? '已盘点' : item.status === 2 ? '有差异' : '待盘点' }}
                  </van-tag>
                </view>

                <view class="item-details">
                  <text class="item-code">编码：{{ item.itemCode }}</text>
                  <text class="item-spec">规格：{{ item.specModel || '无' }}</text>
                </view>

                <view class="item-quantities">
                  <view class="quantity-item">
                    <text class="quantity-label">账面：</text>
                    <text class="quantity-value book">{{ item.bookQuantity }} {{ item.unit }}</text>
                  </view>
                  <view class="quantity-item" wx:if="{{ item.actualQuantity !== null }}">
                    <text class="quantity-label">实盘：</text>
                    <text class="quantity-value actual">{{ item.actualQuantity }} {{ item.unit }}</text>
                  </view>
                  <view class="quantity-item" wx:if="{{ item.differenceQuantity !== null && item.differenceQuantity !== 0 }}">
                    <text class="quantity-label">差异：</text>
                    <text class="quantity-value difference {{ item.differenceQuantity > 0 ? 'positive' : 'negative' }}">
                      {{ item.differenceQuantity > 0 ? '+' : '' }}{{ item.differenceQuantity }} {{ item.unit }}
                    </text>
                  </view>
                </view>

                <view class="item-location" wx:if="{{ item.shelfLocation }}">
                  <van-icon name="location-o" size="12" color="#969799" />
                  <text class="location-text">{{ item.shelfLocation }}</text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="item-actions">
                <van-button
                  type="primary"
                  size="mini"
                  bind:click="onRecordItem"
                  data-detail="{{ item }}"
                  catch:tap="true"
                >
                  {{ item.status === 0 ? '录入' : '修改' }}
                </van-button>
              </view>
            </view>
          </van-cell>
        </block>

        <!-- 加载更多 -->
        <view class="loading-container" wx:if="{{ loading }}">
          <van-loading size="24px">加载中...</van-loading>
        </view>

        <!-- 加载完成 -->
        <view class="finished-container" wx:if="{{ finished && !loading }}">
          <text class="finished-text">已加载全部数据</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view wx:elif="{{ loading }}" class="loading-container">
        <van-loading size="24px">加载中...</van-loading>
      </view>

      <!-- 空状态 -->
      <van-empty
        wx:else
        description="暂无盘点明细数据"
        image="search"
      />
    </scroll-view>
  </view>

  <!-- 录入盘点结果弹窗 -->
  <van-popup
    show="{{ showRecordDialog }}"
    position="bottom"
    round
    bind:close="onCancelRecord"
  >
    <view class="record-dialog">
      <view class="dialog-header">
        <text class="dialog-title">录入盘点结果</text>
        <van-icon name="cross" size="18" bind:click="onCancelRecord" />
      </view>

      <view class="dialog-content" wx:if="{{ currentDetail }}">
        <!-- 物品信息 -->
        <view class="item-info-card">
          <view class="info-row">
            <text class="info-label">物品名称：</text>
            <text class="info-value">{{ currentDetail.itemName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">物品编码：</text>
            <text class="info-value">{{ currentDetail.itemCode }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">账面数量：</text>
            <text class="info-value">{{ currentDetail.bookQuantity }} {{ currentDetail.unit }}</text>
          </view>
          <view class="info-row" wx:if="{{ currentDetail.shelfLocation }}">
            <text class="info-label">货架位置：</text>
            <text class="info-value">{{ currentDetail.shelfLocation }}</text>
          </view>
        </view>

        <!-- 录入表单 -->
        <van-cell-group>
          <van-field
            label="实盘数量"
            value="{{ recordData.actualQuantity }}"
            type="digit"
            placeholder="请输入实盘数量"
            required
            data-field="actualQuantity"
            bind:change="onRecordDataChange"
            right-icon="calculator-o"
          />

          <van-cell
            title="差异原因"
            value="{{ recordData.differenceReason || '请选择差异原因' }}"
            is-link
            bind:click="onShowReasonPicker"
          />
        </van-cell-group>

        <!-- 拍照区域 -->
        <view class="photo-section">
          <view class="section-title">
            <van-icon name="photo-o" size="16" />
            <text>拍照记录</text>
          </view>

          <view class="photo-grid">
            <view
              class="photo-item"
              wx:for="{{ recordData.photos }}"
              wx:key="index"
              bind:click="onPreviewPhoto"
              data-index="{{ index }}"
            >
              <van-image
                src="{{ item }}"
                width="120rpx"
                height="120rpx"
                radius="8rpx"
                fit="cover"
              />
              <view class="photo-delete" bind:click="onDeletePhoto" data-index="{{ index }}" catch:tap="true">
                <van-icon name="cross" size="12" color="#fff" />
              </view>
            </view>

            <view class="photo-add" bind:click="onTakePhoto" wx:if="{{ recordData.photos.length < 3 }}">
              <van-icon name="plus" size="24" color="#c8c9cc" />
              <text class="add-text">拍照</text>
            </view>
          </view>
        </view>
      </view>

      <view class="dialog-footer">
        <van-button
          type="default"
          size="large"
          bind:click="onCancelRecord"
          custom-style="margin-right: 16rpx;"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          size="large"
          bind:click="onSubmitRecord"
          custom-style="flex: 1;"
        >
          保存
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 手动输入弹窗 -->
  <van-popup
    show="{{ showManualDialog }}"
    position="bottom"
    round
    bind:close="onCancelManual"
  >
    <view class="manual-dialog">
      <view class="dialog-header">
        <text class="dialog-title">手动输入物品编码</text>
        <van-icon name="cross" size="18" bind:click="onCancelManual" />
      </view>

      <view class="dialog-content">
        <van-field
          label="物品编码"
          value="{{ manualCode }}"
          placeholder="请输入物品编码"
          required
          bind:change="onManualCodeChange"
          bind:confirm="onSearchByCode"
        />
      </view>

      <view class="dialog-footer">
        <van-button
          type="default"
          size="large"
          bind:click="onCancelManual"
          custom-style="margin-right: 16rpx;"
        >
          取消
        </van-button>
        <van-button
          type="primary"
          size="large"
          bind:click="onSearchByCode"
          custom-style="flex: 1;"
        >
          搜索
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 差异原因选择 -->
  <van-action-sheet
    show="{{ showReasonPicker }}"
    actions="{{ reasonActions }}"
    bind:close="onCloseReasonPicker"
    bind:select="onSelectReason"
    cancel-text="取消"
  />

</view>