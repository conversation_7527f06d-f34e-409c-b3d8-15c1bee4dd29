<!--pages/material/stocktaking/scan.wxml-->
<view class="page-container">
  
  <!-- 扫码区域 -->
  <view class="scan-section">
    <view class="scan-header">
      <text class="scan-title">扫码盘点</text>
      <text class="scan-subtitle">请扫描资产标签上的二维码或条形码</text>
    </view>

    <!-- 扫码按钮 -->
    <view class="scan-actions">
      <van-button 
        type="primary" 
        size="large" 
        icon="scan" 
        bind:click="onScanCode"
        custom-class="scan-btn"
      >
        扫描资产标签
      </van-button>
      
      <van-button 
        type="default" 
        size="large" 
        icon="edit" 
        bind:click="onManualInput"
        custom-class="manual-btn"
      >
        手动输入编码
      </van-button>
    </view>

    <!-- 扫码提示 -->
    <view class="scan-tips">
      <view class="tip-item">
        <van-icon name="info-o" size="16" color="#1989fa" />
        <text class="tip-text">支持二维码和条形码扫描</text>
      </view>
      <view class="tip-item">
        <van-icon name="info-o" size="16" color="#1989fa" />
        <text class="tip-text">确保标签清晰可见，避免反光</text>
      </view>
      <view class="tip-item">
        <van-icon name="info-o" size="16" color="#1989fa" />
        <text class="tip-text">网络不稳定时支持离线盘点</text>
      </view>
    </view>
  </view>

  <!-- 任务进度 -->
  <view class="progress-section">
    <van-cell-group title="任务进度">
      <van-cell 
        title="任务名称" 
        value="{{ taskInfo.taskName }}" 
      />
      <van-cell 
        title="完成进度" 
        value="{{ taskInfo.completedCount }}/{{ taskInfo.totalCount }}" 
      />
      <van-cell 
        title="完成率" 
        value="{{ taskInfo.progressPercent }}%" 
      />
    </van-cell-group>
  </view>

  <!-- 最近扫码记录 -->
  <view class="recent-section">
    <view class="section-title">最近扫码记录</view>
    <view class="recent-list">
      <block wx:if="{{ recentScans.length > 0 }}">
        <view 
          wx:for="{{ recentScans }}" 
          wx:key="scanId"
          class="recent-item"
          bind:tap="onRecentItemClick"
          data-item="{{ item }}"
        >
          <view class="recent-content">
            <view class="recent-header">
              <text class="asset-name">{{ item.assetName }}</text>
              <van-tag 
                type="{{ item.status === 'success' ? 'success' : 'danger' }}" 
                size="small"
              >
                {{ item.status === 'success' ? '成功' : '失败' }}
              </van-tag>
            </view>
            <view class="recent-info">
              <text class="asset-code">{{ item.assetCode }}</text>
              <text class="scan-time">{{ item.scanTime }}</text>
            </view>
          </view>
          <van-icon name="arrow" size="16" color="#c8c9cc" />
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{ recentScans.length === 0 }}" class="empty-recent">
        <van-icon name="records" size="48" color="#c8c9cc" />
        <text class="empty-text">暂无扫码记录</text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <van-button 
      type="default" 
      size="small" 
      bind:click="onViewTaskDetail"
    >
      查看任务详情
    </van-button>
    
    <van-button 
      type="default" 
      size="small" 
      bind:click="onViewProgress"
    >
      查看进度
    </van-button>
  </view>

  <!-- 手动输入弹窗 -->
  <van-popup 
    show="{{ showManualDialog }}" 
    position="center" 
    round
    bind:close="onCancelManual"
  >
    <view class="manual-dialog">
      <view class="dialog-header">
        <text class="dialog-title">手动输入资产编码</text>
      </view>
      
      <view class="dialog-content">
        <van-field
          value="{{ manualCode }}"
          placeholder="请输入资产编码"
          bind:change="onManualCodeChange"
          clearable
          maxlength="50"
        />
      </view>
      
      <view class="dialog-actions">
        <van-button 
          type="default" 
          size="small" 
          bind:click="onCancelManual"
        >
          取消
        </van-button>
        <van-button 
          type="primary" 
          size="small" 
          bind:click="onConfirmManual"
        >
          确定
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 加载状态 -->
  <van-popup 
    show="{{ loading }}" 
    position="center"
    overlay="{{ false }}"
  >
    <view class="loading-dialog">
      <van-loading size="24px" color="#1989fa">处理中...</van-loading>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
