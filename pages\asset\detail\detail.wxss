/* pages/asset/detail/detail.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 详情内容 */
.detail-content {
  padding: 16rpx;
  padding-bottom: 40rpx;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  background: #fafafa;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.card-content {
  padding: 24rpx;
}

.card-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.card-error {
  padding: 40rpx 0;
}

/* 资产图片 */
.asset-images {
  margin-bottom: 32rpx;
}

.image-scroll {
  width: 100%;
  white-space: nowrap;
}

.image-list {
  display: inline-flex;
  gap: 16rpx;
}

.image-item {
  flex-shrink: 0;
}

/* 资产标题 */
.asset-header {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.asset-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.asset-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.asset-code {
  font-size: 28rpx;
  color: #646566;
}

/* 标签页内容 */
.tab-content {
  padding: 16rpx;
}

/* 维护记录区域 */
.maintenance-section {
  margin-bottom: 32rpx;
}

.maintenance-section:last-child {
  margin-bottom: 0;
}

/* 维护计划信息 */
.plan-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  width: 100%;
}

.plan-details {
  display: flex;
  gap: 24rpx;
}

.plan-frequency,
.plan-responsible {
  font-size: 24rpx;
  color: #646566;
}

.plan-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.next-date {
  font-size: 24rpx;
  color: #1989fa;
}

/* 维护任务信息 */
.task-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  width: 100%;
}

.task-details {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-responsible {
  font-size: 24rpx;
  color: #646566;
}

.task-dates {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.scheduled-date {
  font-size: 24rpx;
  color: #969799;
}

.completed-date {
  font-size: 24rpx;
  color: #07c160;
}

.task-notes {
  margin-top: 8rpx;
}

.notes-text {
  font-size: 24rpx;
  color: #646566;
  line-height: 1.4;
}

/* 空状态提示 */
.empty-maintenance,
.no-tasks,
.no-plans {
  padding: 40rpx 0;
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}

/* 关联备件信息 */
.part-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  width: 100%;
}

.part-details {
  display: flex;
  gap: 24rpx;
}

.part-type,
.part-supplier {
  font-size: 24rpx;
  color: #646566;
}

.part-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-text {
  font-size: 26rpx;
  color: #1989fa;
  font-weight: 600;
}

.price-text {
  font-size: 26rpx;
  color: #ee0a24;
  font-weight: 600;
}

.part-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-text,
.min-stock-text {
  font-size: 24rpx;
  color: #969799;
}

.part-description {
  margin-top: 8rpx;
}

.description-text {
  font-size: 24rpx;
  color: #646566;
  line-height: 1.4;
}

/* 图片预览弹窗 */
.image-preview-popup {
  background: transparent !important;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  padding: 32rpx;
}

.preview-actions {
  display: flex;
  gap: 16rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .basic-info-section {
    padding: 24rpx;
  }
  
  .asset-name {
    font-size: 36rpx;
  }
  
  .asset-code {
    font-size: 26rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .basic-info-section {
    background-color: #2a2a2a;
  }
  
  .asset-name {
    color: #ffffff;
  }
  
  .asset-code {
    color: #cccccc;
  }
  
  .task-date {
    color: #999999;
  }
}
