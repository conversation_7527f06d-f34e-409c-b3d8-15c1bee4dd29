// pages/material/stocktaking/history.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { StocktakingAPI } from '../../../utils/stocktaking-api.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    
    // 筛选条件
    filterData: {
      startDate: '',
      endDate: '',
      status: '',
      hasDifference: ''
    },
    
    // 临时筛选数据
    tempFilterData: {
      startDate: '',
      endDate: '',
      status: '',
      hasDifference: ''
    },
    
    // 历史记录列表
    historyList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 20,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 弹窗状态
    showFilterDialog: false,
    showDatePicker: false,
    datePickerType: '', // 'start' 或 'end'
    currentDate: new Date().getTime(),
    
    // 状态选项
    statusOptions: [
      { label: '全部', value: '' },
      { label: '正常', value: 'normal' },
      { label: '有差异', value: 'difference' }
    ],
    
    // 状态映射
    statusMap: {
      'normal': '正常',
      'difference': '有差异'
    }
  },

  /**
   * 计算是否有筛选条件
   */
  get hasFilter() {
    const { filterData } = this.data
    return filterData.startDate || filterData.status || filterData.hasDifference !== ''
  },



  onLoad() {
    console.log('📚 盘点历史页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📚 初始化盘点历史页面')
    this.loadHistoryList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 重置分页信息
      this.setData({
        'pageInfo.current': 1,
        historyList: [],
        finished: false
      })
      
      // 重新加载数据
      await this.loadHistoryList(true)
      
      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.refreshData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadHistoryList()
  },

  /**
   * 加载历史记录列表
   */
  async loadHistoryList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword?.trim()) {
        params.keyword = this.data.searchKeyword.trim()
      }

      // 添加筛选条件
      const { filterData } = this.data
      if (filterData.startDate) {
        params.startDate = filterData.startDate
      }
      if (filterData.endDate) {
        params.endDate = filterData.endDate
      }
      if (filterData.status) {
        params.status = filterData.status
      }
      if (filterData.hasDifference !== '') {
        params.hasDifference = filterData.hasDifference
      }

      console.log('📚 盘点历史查询参数:', params)

      // 调用真实API
      const response = await StocktakingAPI.getMyHistory(params)
      
      if (response && response.code === 200) {
        // 适配后端返回格式
        const records = response.rows || response.data?.records || []
        const total = response.total || response.data?.total || 0

        console.log('📚 获取到盘点历史列表:', records)

        // 更新列表数据
        let newList = []
        if (isRefresh || this.data.pageInfo.current === 1) {
          // 刷新或首次加载，替换数据
          newList = records
        } else {
          // 加载更多，追加数据
          newList = [...this.data.historyList, ...records]
        }

        // 计算分页信息
        const pageSize = this.data.pageInfo.size
        const currentPage = this.data.pageInfo.current
        const totalPages = Math.ceil(total / pageSize)

        // 更新页面数据
        this.setData({
          historyList: newList,
          'pageInfo.total': total,
          'pageInfo.size': pageSize,
          'pageInfo.current': currentPage,
          'pageInfo.pages': totalPages,
          finished: (currentPage >= totalPages) || (records.length === 0)
        })

        console.log('✅ 盘点历史列表加载完成，共', newList.length, '条记录')
      } else {
        throw new Error(response?.msg || '获取盘点历史失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点历史失败:', error)
      
      // 如果是首次加载失败，显示错误信息
      if (this.data.pageInfo.current === 1) {
        Toast.fail('获取数据失败')
      }
      
      // 恢复页码
      if (this.data.pageInfo.current > 1) {
        this.setData({
          'pageInfo.current': this.data.pageInfo.current - 1
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },



  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })

    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行历史记录搜索，关键词:', keyword)

    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      historyList: [],
      finished: false
    })

    // 重新加载数据
    this.loadHistoryList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 显示筛选弹窗
   */
  onShowFilter() {
    // 复制当前筛选条件到临时数据
    this.setData({
      tempFilterData: { ...this.data.filterData },
      showFilterDialog: true
    })
  },

  /**
   * 关闭筛选弹窗
   */
  onCloseFilter() {
    this.setData({
      showFilterDialog: false
    })
  },

  /**
   * 选择开始日期
   */
  onSelectStartDate() {
    this.setData({
      datePickerType: 'start',
      showDatePicker: true,
      currentDate: this.data.tempFilterData.startDate ?
        new Date(this.data.tempFilterData.startDate).getTime() :
        new Date().getTime()
    })
  },

  /**
   * 选择结束日期
   */
  onSelectEndDate() {
    this.setData({
      datePickerType: 'end',
      showDatePicker: true,
      currentDate: this.data.tempFilterData.endDate ?
        new Date(this.data.tempFilterData.endDate).getTime() :
        new Date().getTime()
    })
  },

  /**
   * 关闭日期选择器
   */
  onCloseDatePicker() {
    this.setData({
      showDatePicker: false
    })
  },

  /**
   * 确认日期选择
   */
  onConfirmDate(event) {
    const date = new Date(event.detail)
    const dateString = date.toISOString().split('T')[0]

    if (this.data.datePickerType === 'start') {
      this.setData({
        'tempFilterData.startDate': dateString
      })
    } else {
      this.setData({
        'tempFilterData.endDate': dateString
      })
    }

    this.setData({
      showDatePicker: false
    })
  },

  /**
   * 选择状态
   */
  onSelectStatus(event) {
    const value = event.currentTarget.dataset.value
    this.setData({
      'tempFilterData.status': value
    })
  },

  /**
   * 选择差异情况
   */
  onSelectDifference(event) {
    const value = event.currentTarget.dataset.value
    this.setData({
      'tempFilterData.hasDifference': value
    })
  },

  /**
   * 重置筛选
   */
  onResetFilter() {
    this.setData({
      tempFilterData: {
        startDate: '',
        endDate: '',
        status: '',
        hasDifference: ''
      }
    })
  },

  /**
   * 确认筛选
   */
  onConfirmFilter() {
    // 应用筛选条件
    this.setData({
      filterData: { ...this.data.tempFilterData },
      showFilterDialog: false
    })

    // 重新加载数据
    this.refreshData()
  },

  /**
   * 清除日期筛选
   */
  onClearDateFilter() {
    this.setData({
      'filterData.startDate': '',
      'filterData.endDate': ''
    })
    this.refreshData()
  },

  /**
   * 清除状态筛选
   */
  onClearStatusFilter() {
    this.setData({
      'filterData.status': ''
    })
    this.refreshData()
  },

  /**
   * 清除差异筛选
   */
  onClearDifferenceFilter() {
    this.setData({
      'filterData.hasDifference': ''
    })
    this.refreshData()
  },

  /**
   * 清空所有筛选
   */
  onClearAllFilter() {
    this.setData({
      filterData: {
        startDate: '',
        endDate: '',
        status: '',
        hasDifference: ''
      }
    })
    this.refreshData()
  },

  /**
   * 历史记录项点击
   */
  onHistoryItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击历史记录:', item)

    // 显示记录详情
    wx.showModal({
      title: '盘点记录详情',
      content: `资产：${item.assetName}\n编码：${item.assetCode}\n位置：${item.actualLocation}\n状态：${item.actualStatus}\n盘点人：${item.operatorName}\n时间：${item.recordTime}${item.hasDifference ? '\n差异：' + item.differenceReason : ''}`,
      showCancel: false
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
