/* pages/maintenance/task/execute.wxss */
.task-execute-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* 步骤导航 */
.steps-container {
  background-color: #fff;
  padding: 24rpx;
  margin-bottom: 16rpx;
}

/* 步骤内容 */
.step-content {
  margin: 0 24rpx 24rpx;
}

.step-panel {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

/* 维护事项 */
.maintenance-items {
  margin-top: 24rpx;
}

.items-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.items-content {
  padding: 16rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 表单错误信息 */
.error-message {
  font-size: 24rpx;
  color: #ee0a24;
  padding: 8rpx 16rpx 0;
}

/* 备件列表 */
.parts-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.part-item {
  padding: 20rpx;
  background-color: #f7f8fa;
  border-radius: 12rpx;
}

.part-header {
  margin-bottom: 16rpx;
}

.part-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.part-spec {
  font-size: 24rpx;
  color: #646566;
  margin-top: 4rpx;
}

.part-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.info-label {
  font-size: 24rpx;
  color: #969799;
}

.info-value {
  font-size: 24rpx;
  color: #323233;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stock-warning {
  color: #ee0a24;
}

.part-usage {
  margin-bottom: 16rpx;
}

.usage-title {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 8rpx;
}

.part-quantity {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantity-title {
  font-size: 24rpx;
  color: #969799;
}

.empty-parts {
  padding: 32rpx 0;
}

.empty-tip {
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}

/* 拍照功能 */
.photo-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.photo-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.photo-item {
  background-color: #f7f8fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.photo-image {
  position: relative;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 48rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.empty-photos {
  padding: 32rpx 0;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.step-buttons {
  display: flex;
  gap: 16rpx;
}

.step-buttons van-button {
  flex: 1;
}

/* 空状态 */
.empty-container {
  padding: 64rpx 0;
}

.empty-tips {
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}
