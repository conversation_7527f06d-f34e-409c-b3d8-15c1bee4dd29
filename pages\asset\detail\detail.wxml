<!--pages/asset/detail/detail.wxml-->
<view class="page-container">

  <!-- 资产详情内容 -->
  <view class="detail-content">

    <!-- 基本信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">基本信息</text>
        <van-loading wx:if="{{ basicInfoLoading }}" type="spinner" size="16px" />
      </view>

      <view wx:if="{{ basicInfoLoading }}" class="card-loading">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <view wx:elif="{{ assetDetail }}" class="card-content">
        <!-- 资产图片 -->
        <view class="asset-images" wx:if="{{ assetDetail.images && assetDetail.images.length > 0 }}">
          <scroll-view class="image-scroll" scroll-x>
            <view class="image-list">
              <view
                class="image-item"
                wx:for="{{ assetDetail.images }}"
                wx:key="index"
                bind:tap="previewImage"
                data-index="{{ index }}"
              >
                <van-image
                  src="{{ item }}"
                  width="160rpx"
                  height="160rpx"
                  radius="12rpx"
                  fit="cover"
                  error-icon="photo-fail"
                />
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 资产标题信息 -->
        <view class="asset-header">
          <view class="asset-title">
            <text class="asset-name">{{ assetDetail.assetName }}</text>
            <van-tag
              type="{{ assetDetail.statusType }}"
              size="medium"
            >
              {{ assetDetail.statusName }}
            </van-tag>
          </view>
          <text class="asset-code">编号: {{ assetDetail.assetId }}</text>
        </view>

        <!-- 基本信息详情 -->
        <van-cell-group title="基本信息">
          <van-cell title="资产名称" value="{{ assetDetail.assetName }}" />
          <van-cell title="资产编号" value="{{ assetDetail.assetId }}" />
          <van-cell title="所属部门" value="{{ assetDetail.deptName || '未设置' }}" />
          <van-cell title="资产管理员" value="{{ assetDetail.managersText }}" />
          <van-cell title="存放位置" value="{{ assetDetail.locationText }}" />
          <van-cell title="资产单位" value="{{ assetDetail.assetUnit || '未设置' }}" />
        </van-cell-group>

        <!-- 规格信息 -->
        <van-cell-group title="规格信息">
          <van-cell title="规格型号" value="{{ assetDetail.specModel || '未设置' }}" />
          <van-cell title="资产品牌" value="{{ assetDetail.assetBrand || '未设置' }}" />
          <van-cell title="资产用途" value="{{ assetDetail.assetPurpose || '未设置' }}" />
          <van-cell
            wx:if="{{ assetDetail.remark }}"
            title="备注说明"
            value="{{ assetDetail.remark }}"
            label="详细说明信息"
          />
        </van-cell-group>

        <!-- 附件信息 -->
        <van-cell-group wx:if="{{ assetDetail.attachments && assetDetail.attachments.length > 0 }}" title="相关附件">
          <van-cell
            wx:for="{{ assetDetail.attachments }}"
            wx:key="index"
            title="{{ item.fileName }}"
            label="文件类型: {{ item.fileType }}"
            is-link
            bind:click="viewAttachment"
            data-attachment="{{ item }}"
          >
            <van-icon slot="icon" name="description" size="20" color="#1989fa" />
          </van-cell>
        </van-cell-group>
      </view>

      <view wx:else class="card-error">
        <van-empty
          image="error"
          description="基本信息加载失败"
        />
      </view>
    </view>

    <!-- 维护信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">维护信息</text>
        <van-loading wx:if="{{ maintenanceLoading }}" type="spinner" size="16px" />
      </view>

      <view wx:if="{{ maintenanceLoading }}" class="card-loading">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <view wx:else class="card-content">
        <!-- 维护计划 -->
        <van-cell-group title="维护计划" wx:if="{{ maintenancePlans.length > 0 }}">
          <van-cell
            wx:for="{{ maintenancePlans }}"
            wx:key="planId"
            title="{{ item.planName }}"
            label="{{ item.description }}"
            use-slot
            is-link
            bind:click="viewMaintenancePlan"
            data-plan="{{ item }}"
          >
            <view class="plan-info">
              <view class="plan-details">
                <text class="plan-frequency">频率: {{ item.frequency || '未设置' }}</text>
                <text class="plan-responsible">负责人: {{ item.responsible || '未设置' }}</text>
              </view>
              <view class="plan-status">
                <van-tag
                  type="{{ item.status === 'active' ? 'success' : 'default' }}"
                  size="small"
                >
                  {{ item.status === 'active' ? '启用' : '停用' }}
                </van-tag>
                <text class="next-date">下次: {{ item.nextMaintenanceDate || '未设置' }}</text>
              </view>
            </view>
          </van-cell>
        </van-cell-group>

        <!-- 维护任务 -->
        <van-cell-group title="维护任务" wx:if="{{ maintenanceTasks.length > 0 }}">
          <van-cell
            wx:for="{{ maintenanceTasks }}"
            wx:key="taskId"
            title="{{ item.taskName }}"
            label="{{ item.description }}"
            use-slot
            is-link
            bind:click="viewMaintenanceTask"
            data-task="{{ item }}"
          >
            <view class="task-info">
              <view class="task-details">
                <van-tag
                  type="{{ item.statusType }}"
                  size="small"
                >
                  {{ item.statusName }}
                </van-tag>
                <text class="task-responsible">负责人: {{ item.responsible || '未设置' }}</text>
              </view>
              <view class="task-dates">
                <text class="scheduled-date">计划: {{ item.formattedScheduledDate }}</text>
                <text wx:if="{{ item.isCompleted && item.formattedCompletedDate }}" class="completed-date">
                  完成: {{ item.formattedCompletedDate }}
                </text>
              </view>
              <view wx:if="{{ item.notes }}" class="task-notes">
                <text class="notes-text">备注: {{ item.notes }}</text>
              </view>
            </view>
          </van-cell>
        </van-cell-group>

        <!-- 无维护记录提示 -->
        <view wx:if="{{ maintenancePlans.length === 0 && maintenanceTasks.length === 0 }}" class="empty-maintenance">
          <van-empty
            image="search"
            description="暂无维护记录"
          >
            <text class="empty-tip">该资产尚未制定维护计划或执行维护任务</text>
          </van-empty>
        </view>

        <!-- 仅有计划无任务 -->
        <view wx:elif="{{ maintenancePlans.length > 0 && maintenanceTasks.length === 0 }}" class="no-tasks">
          <van-cell-group title="维护任务">
            <van-empty
              image="search"
              description="暂无维护任务"
            >
              <text class="empty-tip">已制定维护计划，但尚未生成具体任务</text>
            </van-empty>
          </van-cell-group>
        </view>

        <!-- 仅有任务无计划 -->
        <view wx:elif="{{ maintenancePlans.length === 0 && maintenanceTasks.length > 0 }}" class="no-plans">
          <van-cell-group title="维护计划">
            <van-empty
              image="search"
              description="暂无维护计划"
            >
              <text class="empty-tip">存在维护任务，但未制定长期维护计划</text>
            </van-empty>
          </van-cell-group>
        </view>
      </view>
    </view>

    <!-- 关联备件卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">关联备件</text>
        <van-loading wx:if="{{ partsLoading }}" type="spinner" size="16px" />
      </view>

      <view wx:if="{{ partsLoading }}" class="card-loading">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <view wx:else class="card-content">
        <van-cell-group wx:if="{{ assetParts.length > 0 }}">
          <van-cell
            wx:for="{{ assetParts }}"
            wx:key="relationId"
            title="{{ item.partName }}"
            label="编码: {{ item.partCode }} | 型号: {{ item.model || '未设置' }}"
            use-slot
            is-link
            bind:click="viewPartDetail"
            data-part="{{ item }}"
          >
            <view class="part-info">
              <view class="part-details">
                <text class="part-type">类型: {{ item.partType || '未设置' }}</text>
                <text class="part-supplier">供应商: {{ item.supplier || '未设置' }}</text>
              </view>
              <view class="part-quantity">
                <text class="quantity-text">数量: {{ item.quantity || 0 }}</text>
                <text class="price-text">单价: ¥{{ item.unitPrice ? Number(item.unitPrice).toFixed(2) : '0.00' }}</text>
              </view>
              <view class="part-stock">
                <text class="stock-text">库存: {{ item.stockQuantity || 0 }}</text>
                <text class="min-stock-text">最低库存: {{ item.minStockLevel || 0 }}</text>
              </view>
              <view wx:if="{{ item.description }}" class="part-description">
                <text class="description-text">{{ item.description }}</text>
              </view>
            </view>
          </van-cell>
        </van-cell-group>

        <!-- 无关联备件 -->
        <van-empty
          wx:if="{{ assetParts.length === 0 }}"
          image="search"
          description="暂无关联备件"
        >
          <text class="empty-tip">该资产尚未关联任何备件信息</text>
        </van-empty>
      </view>
    </view>

  </view>

  <!-- 错误状态 -->
  <van-empty
    wx:if="{{ hasError }}"
    image="error"
    description="加载失败，请重试"
  >
    <van-button
      round
      type="primary"
      size="small"
      bind:tap="onRetryLoad"
    >
      重新加载
    </van-button>
  </van-empty>



  <!-- 图片预览弹窗 -->
  <van-popup
    show="{{ showImagePreview }}"
    position="center"
    round
    bind:close="closeImagePreview"
    custom-class="image-preview-popup"
  >
    <view class="image-preview">
      <van-image
        src="{{ previewImageUrl }}"
        width="600rpx"
        height="600rpx"
        fit="contain"
        error-icon="photo-fail"
      />
      <view class="preview-actions">
        <van-button
          type="default"
          size="small"
          bind:tap="closeImagePreview"
        >
          关闭
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
