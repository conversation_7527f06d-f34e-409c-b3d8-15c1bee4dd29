/* pages/material/stocktaking/history.wxss */
.page-container {
  height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 搜索和筛选区域 */
.search-section {
  background-color: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

/* 筛选标签 */
.filter-tags {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  border-top: 1rpx solid #f2f3f5;
}

.filter-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  flex: 1;
}

.clear-all {
  color: #1989fa;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
}

/* 列表区域 */
.list-section {
  flex: 1;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
}

.history-list {
  padding: 16rpx;
}

/* 历史记录项 */
.history-item {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.history-content {
  padding: 24rpx;
}

/* 记录头部 */
.history-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.asset-info {
  flex: 1;
  margin-right: 16rpx;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  display: block;
  margin-bottom: 8rpx;
}

.asset-code {
  font-size: 26rpx;
  color: #646566;
}

.record-tags {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

/* 记录信息 */
.history-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-text {
  font-size: 26rpx;
  color: #646566;
  margin-left: 8rpx;
}

/* 差异信息 */
.difference-info {
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.difference-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.difference-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #ee0a24;
}

.difference-content {
  padding-left: 24rpx;
}

.difference-text {
  font-size: 24rpx;
  color: #ee0a24;
  line-height: 1.4;
}

/* 现场记录 */
.record-info {
  border-top: 1rpx solid #f2f3f5;
  padding-top: 16rpx;
}

.record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.indicator-text {
  font-size: 24rpx;
  color: #1989fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  color: #969799;
  font-size: 24rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  color: #c8c9cc;
  font-size: 24rpx;
}

/* 筛选弹窗 */
.filter-dialog {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.filter-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
  margin-bottom: 16rpx;
}

/* 日期范围 */
.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-separator {
  font-size: 26rpx;
  color: #646566;
}

/* 状态选项 */
.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.status-option {
  padding: 16rpx 24rpx;
  border: 1rpx solid #ebedf0;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #646566;
  background-color: #fff;
  transition: all 0.2s;
}

.status-option.active {
  border-color: #1989fa;
  color: #1989fa;
  background-color: #f0f9ff;
}

/* 差异选项 */
.difference-options {
  display: flex;
  gap: 16rpx;
}

.difference-option {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 1rpx solid #ebedf0;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #646566;
  background-color: #fff;
  text-align: center;
  transition: all 0.2s;
}

.difference-option.active {
  border-color: #1989fa;
  color: #1989fa;
  background-color: #f0f9ff;
}

/* 筛选操作按钮 */
.filter-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #ebedf0;
}

.filter-actions .van-button {
  flex: 1;
}
