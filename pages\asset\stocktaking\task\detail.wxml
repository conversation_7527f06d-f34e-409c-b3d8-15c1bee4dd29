<!--pages/material/stocktaking/task/detail.wxml-->
<view class="page-container">
  
  <!-- 任务基本信息 -->
  <view class="task-info-section">
    <view class="task-header">
      <view class="task-title">
        <text class="task-name">{{ taskInfo.taskName }}</text>
        <van-tag 
          type="{{ statusMap[taskInfo.status].type }}" 
          size="medium"
          color="{{ statusMap[taskInfo.status].color }}"
        >
          {{ statusMap[taskInfo.status].label }}
        </van-tag>
      </view>
      <view class="task-code">任务编号：{{ taskInfo.taskCode }}</view>
    </view>

    <van-cell-group>
      <van-cell title="盘点计划" value="{{ taskInfo.planName }}" />
      <van-cell title="盘点范围" value="{{ taskInfo.scopeDescription }}" />
      <van-cell title="分配时间" value="{{ taskInfo.assignTime }}" />
      <van-cell title="截止时间" value="{{ taskInfo.deadlineTime }}" />
      <van-cell 
        wx:if="{{ taskInfo.startTime }}"
        title="开始时间" 
        value="{{ taskInfo.startTime }}" 
      />
      <van-cell 
        wx:if="{{ taskInfo.completeTime }}"
        title="完成时间" 
        value="{{ taskInfo.completeTime }}" 
      />
    </van-cell-group>

    <!-- 进度信息 -->
    <view class="progress-section">
      <view class="progress-header">
        <text class="progress-title">完成进度</text>
        <text class="progress-text">{{ taskInfo.completedCount }}/{{ taskInfo.totalCount }} ({{ taskInfo.progressPercent }}%)</text>
      </view>
      <van-progress 
        percentage="{{ taskInfo.progressPercent }}" 
        stroke-width="8" 
        color="{{ taskInfo.progressPercent === 100 ? '#07c160' : '#1989fa' }}"
        track-color="#f2f3f5"
      />
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <van-button 
        wx:if="{{ taskInfo.status === 'pending' }}"
        type="primary" 
        block
        bind:click="onStartTask"
      >
        开始盘点
      </van-button>
      
      <van-button 
        wx:if="{{ taskInfo.status === 'running' }}"
        type="primary" 
        block
        bind:click="onContinueTask"
      >
        继续盘点
      </van-button>
      
      <van-button 
        wx:if="{{ taskInfo.status === 'running' }}"
        type="default" 
        block
        bind:click="onPauseTask"
        custom-class="pause-btn"
      >
        暂停任务
      </van-button>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-section">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
      <van-tab title="资产清单" name="assets"></van-tab>
      <van-tab title="盘点记录" name="records"></van-tab>
      <van-tab title="差异汇总" name="differences"></van-tab>
    </van-tabs>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    
    <!-- 资产清单 -->
    <view wx:if="{{ activeTab === 'assets' }}" class="assets-tab">
      <!-- 搜索栏 -->
      <view class="search-section">
        <van-search
          value="{{ searchKeyword }}"
          placeholder="搜索资产名称或编码"
          bind:search="onSearch"
          bind:input="onSearchInput"
          bind:clear="onSearchClear"
        />
      </view>

      <!-- 资产列表 -->
      <scroll-view 
        class="scroll-container"
        scroll-y
        refresher-enabled
        refresher-triggered="{{ refreshing }}"
        bind:refresherrefresh="onRefresh"
        bind:scrolltolower="onLoadMore"
      >
        <view class="asset-list">
          <block wx:if="{{ assetList.length > 0 }}">
            <van-cell
              wx:for="{{ assetList }}"
              wx:key="assetId"
              bind:click="onAssetItemClick"
              data-asset="{{ item }}"
              custom-class="asset-item"
              use-slot
            >
              <view class="asset-content">
                <view class="asset-header">
                  <text class="asset-name">{{ item.assetName }}</text>
                  <van-tag 
                    type="{{ item.status === 'completed' ? 'success' : (item.status === 'pending' ? 'warning' : 'primary') }}"
                    size="small"
                  >
                    {{ item.status === 'completed' ? '已盘点' : (item.status === 'pending' ? '待盘点' : '有差异') }}
                  </van-tag>
                </view>
                <view class="asset-info">
                  <view class="info-row">
                    <text class="label">资产编码：</text>
                    <text class="value">{{ item.assetCode }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">存放位置：</text>
                    <text class="value">{{ item.location }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">资产状态：</text>
                    <text class="value">{{ item.assetStatus }}</text>
                  </view>
                </view>
              </view>
            </van-cell>
          </block>

          <!-- 空状态 -->
          <van-empty 
            wx:if="{{ !loading && assetList.length === 0 }}"
            image="search" 
            description="{{ searchKeyword ? '未找到相关资产' : '暂无资产数据' }}"
          />

          <!-- 加载状态 -->
          <view wx:if="{{ loading && assetList.length === 0 }}" class="loading-container">
            <van-loading size="24px">加载中...</van-loading>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 盘点记录 -->
    <view wx:if="{{ activeTab === 'records' }}" class="records-tab">
      <scroll-view class="scroll-container" scroll-y>
        <view class="record-list">
          <block wx:if="{{ recordList.length > 0 }}">
            <van-cell
              wx:for="{{ recordList }}"
              wx:key="recordId"
              custom-class="record-item"
              use-slot
            >
              <view class="record-content">
                <view class="record-header">
                  <text class="asset-name">{{ item.assetName }}</text>
                  <text class="record-time">{{ item.recordTime }}</text>
                </view>
                <view class="record-info">
                  <view class="info-row">
                    <text class="label">资产编码：</text>
                    <text class="value">{{ item.assetCode }}</text>
                  </view>
                  <view class="info-row">
                    <text class="label">盘点结果：</text>
                    <text class="value {{ item.hasDifference ? 'difference' : 'normal' }}">
                      {{ item.hasDifference ? '有差异' : '正常' }}
                    </text>
                  </view>
                  <view wx:if="{{ item.differenceReason }}" class="info-row">
                    <text class="label">差异原因：</text>
                    <text class="value">{{ item.differenceReason }}</text>
                  </view>
                </view>
              </view>
            </van-cell>
          </block>

          <!-- 空状态 -->
          <van-empty 
            wx:if="{{ recordList.length === 0 }}"
            image="search" 
            description="暂无盘点记录"
          />
        </view>
      </scroll-view>
    </view>

    <!-- 差异汇总 -->
    <view wx:if="{{ activeTab === 'differences' }}" class="differences-tab">
      <scroll-view class="scroll-container" scroll-y>
        <view class="difference-summary">
          <!-- 差异统计 -->
          <van-cell-group title="差异统计">
            <van-cell title="盘盈数量" value="{{ differenceStats.surplus || 0 }}" />
            <van-cell title="盘亏数量" value="{{ differenceStats.deficit || 0 }}" />
            <van-cell title="状态差异" value="{{ differenceStats.statusDiff || 0 }}" />
            <van-cell title="位置差异" value="{{ differenceStats.locationDiff || 0 }}" />
          </van-cell-group>

          <!-- 差异明细 -->
          <view class="difference-list">
            <view class="list-title">差异明细</view>
            <block wx:if="{{ differenceList.length > 0 }}">
              <van-cell
                wx:for="{{ differenceList }}"
                wx:key="diffId"
                custom-class="difference-item"
                use-slot
              >
                <view class="difference-content">
                  <view class="diff-header">
                    <text class="asset-name">{{ item.assetName }}</text>
                    <van-tag 
                      type="danger" 
                      size="small"
                    >
                      {{ item.differenceType }}
                    </van-tag>
                  </view>
                  <view class="diff-info">
                    <view class="info-row">
                      <text class="label">资产编码：</text>
                      <text class="value">{{ item.assetCode }}</text>
                    </view>
                    <view class="info-row">
                      <text class="label">差异原因：</text>
                      <text class="value">{{ item.differenceReason }}</text>
                    </view>
                    <view class="info-row">
                      <text class="label">发现时间：</text>
                      <text class="value">{{ item.foundTime }}</text>
                    </view>
                  </view>
                </view>
              </van-cell>
            </block>

            <!-- 空状态 -->
            <van-empty 
              wx:if="{{ differenceList.length === 0 }}"
              image="search" 
              description="暂无差异记录"
            />
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
