/* pages/material/stocktaking/record.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 扫码区域 */
.scan-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.scan-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 16rpx;
  border-radius: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.scan-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.scan-icon {
  margin-bottom: 16rpx;
}

.scan-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 列表区域 */
.list-section {
  flex: 1;
  height: calc(100vh - 240rpx);
}

.scroll-container {
  height: 100%;
  padding: 0 16rpx;
}

/* 重写 van-cell 样式 */
.detail-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.item-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.item-image {
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.item-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.item-code,
.item-spec {
  font-size: 24rpx;
  color: #666;
}

.item-quantities {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.quantity-item {
  display: flex;
  align-items: center;
}

.quantity-label {
  font-size: 24rpx;
  color: #999;
}

.quantity-value {
  font-size: 26rpx;
  font-weight: 600;
  margin-left: 4rpx;
}

.quantity-value.book {
  color: #1989fa;
}

.quantity-value.actual {
  color: #07c160;
}

.quantity-value.difference.positive {
  color: #ff976a;
}

.quantity-value.difference.negative {
  color: #ee0a24;
}

.item-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #969799;
}

.item-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 加载状态 */
.loading-container,
.finished-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
}

.finished-text {
  font-size: 24rpx;
  color: #999;
}

/* 录入弹窗样式 */
.record-dialog,
.manual-dialog {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.dialog-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #ebedf0;
  gap: 16rpx;
}

/* 物品信息卡片 */
.item-info-card {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

/* 拍照区域 */
.photo-section {
  margin-top: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.section-title text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-add {
  width: 120rpx;
  height: 120rpx;
  border: 2rpx dashed #c8c9cc;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #c8c9cc;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .item-content {
    padding: 20rpx;
    gap: 20rpx;
  }

  .item-name {
    font-size: 30rpx;
  }

  .quantity-value {
    font-size: 24rpx;
  }

  .dialog-content {
    padding: 24rpx;
  }

  .photo-item,
  .photo-add {
    width: 100rpx;
    height: 100rpx;
  }
}