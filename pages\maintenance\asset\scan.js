// pages/maintenance/asset/scan.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'

Page({
  data: {
    // 扫码结果
    scanResult: '',
    assetId: '',
    
    // 资产信息
    assetInfo: null,
    
    // 任务列表
    taskList: [],
    
    // 加载状态
    loading: false,
    
    // 扫码状态
    scanning: false,
    
    // 空状态
    isEmpty: false
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面
    this.initPage()
  },

  onShow() {
    // 页面显示时，如果有资产ID，刷新数据
    if (this.data.assetId) {
      this.loadAssetTasks(this.data.assetId)
    }
  },

  /**
   * 初始化页面
   */
  initPage() {
    console.log('资产扫码页面初始化')
  },

  /**
   * 扫描二维码
   */
  onScanQRCode() {
    this.setData({ scanning: true })
    
    wx.scanCode({
      onlyFromCamera: false,
      scanType: ['qrCode'],
      success: (res) => {
        console.log('扫码结果:', res)
        
        // 解析扫码结果
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        if (error.errMsg !== 'scanCode:fail cancel') {
          Toast.fail('扫码失败')
        }
      },
      complete: () => {
        this.setData({ scanning: false })
      }
    })
  },

  /**
   * 处理扫码结果
   */
  handleScanResult(result) {
    this.setData({ scanResult: result })
    
    try {
      // 尝试解析二维码内容
      // 假设二维码内容格式为：{"assetId": "**********", "type": "asset"}
      // 或者直接是资产ID字符串
      let assetId = ''
      
      try {
        // 尝试解析JSON
        const data = JSON.parse(result)
        if (data.assetId) {
          assetId = data.assetId
        }
      } catch (e) {
        // 如果不是JSON，直接使用结果作为资产ID
        assetId = result
      }
      
      if (!assetId) {
        Toast.fail('无效的资产二维码')
        return
      }
      
      console.log('解析到资产ID:', assetId)
      this.setData({ assetId })
      
      // 加载资产相关任务
      this.loadAssetTasks(assetId)
      
    } catch (error) {
      console.error('解析二维码失败:', error)
      Toast.fail('无效的二维码格式')
    }
  },

  /**
   * 加载资产相关任务
   */
  async loadAssetTasks(assetId) {
    try {
      this.setData({ loading: true })
      
      console.log('加载资产任务:', assetId)
      
      // 模拟API调用 - 实际开发时替换为真实API
      const mockData = this.generateMockData(assetId)
      
      this.setData({
        assetInfo: mockData.assetInfo,
        taskList: mockData.taskList,
        isEmpty: mockData.taskList.length === 0
      })
      
      console.log('资产任务加载成功')
      
    } catch (error) {
      console.error('加载资产任务失败:', error)
      Toast.fail('加载资产任务失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 查看任务详情
   */
  onViewTask(event) {
    const task = event.currentTarget.dataset.task
    console.log('查看任务:', task)
    
    // 跳转到任务详情页
    wx.navigateTo({
      url: `/pages/maintenance/task/detail?taskId=${task.taskId}`,
      fail: (error) => {
        console.error('跳转任务详情失败:', error)
        Toast.fail('跳转任务详情失败')
      }
    })
  },

  /**
   * 查看资产详情
   */
  onViewAsset() {
    const assetId = this.data.assetInfo.assetId
    
    // 跳转到资产详情页
    wx.navigateTo({
      url: `/pages/asset/detail/detail?assetId=${assetId}`,
      fail: (error) => {
        console.error('跳转资产详情失败:', error)
        Toast.fail('跳转资产详情失败')
      }
    })
  },

  /**
   * 生成模拟数据
   */
  generateMockData(assetId) {
    // 根据资产ID返回不同的模拟数据
    const mockDataMap = {
      '**********': {
        assetInfo: {
          assetId: '**********',
          assetName: '中央空调主机',
          assetCode: 'KT-2023-001',
          assetLocation: '设备楼3层机房',
          assetStatus: '在用'
        },
        taskList: [
          {
            taskId: 'MT20250401001',
            taskTitle: '空调系统季度维护',
            scheduledTime: '2025-04-15 09:00:00',
            priority: 3,
            priorityName: '高',
            status: 1,
            statusName: '待执行',
            overdue: false
          }
        ]
      },
      'AS20220001': {
        assetInfo: {
          assetId: 'AS20220001',
          assetName: '1号电梯',
          assetCode: 'DT-2022-001',
          assetLocation: '主楼电梯间',
          assetStatus: '在用'
        },
        taskList: [
          {
            taskId: 'MT20250401002',
            taskTitle: '电梯月度安全检查',
            scheduledTime: '2025-04-10 14:00:00',
            priority: 4,
            priorityName: '紧急',
            status: 2,
            statusName: '执行中',
            overdue: true
          }
        ]
      },
      'AS20210001': {
        assetInfo: {
          assetId: 'AS20210001',
          assetName: '消防泵',
          assetCode: 'XF-2021-003',
          assetLocation: '地下一层消防泵房',
          assetStatus: '在用'
        },
        taskList: []
      }
    }
    
    // 如果找不到对应ID的数据，返回默认数据
    return mockDataMap[assetId] || {
      assetInfo: {
        assetId: assetId,
        assetName: '未知资产',
        assetCode: '未知编码',
        assetLocation: '未知位置',
        assetStatus: '未知状态'
      },
      taskList: []
    }
  }
})
