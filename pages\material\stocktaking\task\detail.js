// pages/material/stocktaking/task/detail.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../../utils/permission.js'
import { StocktakingAPI } from '../../../../utils/stocktaking-api.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务信息
    taskInfo: {},
    
    // 当前标签页
    activeTab: 'assets',
    
    // 搜索关键词
    searchKeyword: '',
    
    // 资产列表
    assetList: [],
    
    // 盘点记录列表
    recordList: [],
    
    // 差异列表
    differenceList: [],
    
    // 差异统计
    differenceStats: {},
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 20,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 状态映射
    statusMap: {
      'pending': { label: '待执行', color: '#ff976a', type: 'warning' },
      'running': { label: '执行中', color: '#1989fa', type: 'primary' },
      'completed': { label: '已完成', color: '#07c160', type: 'success' },
      'paused': { label: '已暂停', color: '#969799', type: 'default' }
    }
  },

  onLoad(options) {
    console.log('📋 任务详情页面加载', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId, tab } = options
    if (!taskId) {
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ 
      taskId,
      activeTab: tab || 'assets'
    })

    // 加载任务详情
    this.loadTaskDetail()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.refreshCurrentTab()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 加载任务详情
   */
  async loadTaskDetail() {
    try {
      // 调用真实API
      const response = await StocktakingAPI.getTaskDetail(this.data.taskId)
      
      if (response && response.code === 200) {
        // 适配后端返回格式
        const taskInfo = response.data || response.rows?.[0] || {}
        this.setData({
          taskInfo: taskInfo
        })

        // 加载当前标签页数据
        this.loadCurrentTabData()
      } else {
        throw new Error(response?.msg || '获取任务详情失败')
      }
    } catch (error) {
      console.error('❌ 加载任务详情失败:', error)
      Toast.fail('获取任务详情失败')
    }
  },



  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.name
    console.log('切换标签页:', activeTab)
    
    this.setData({ activeTab })
    this.loadCurrentTabData()
  },

  /**
   * 加载当前标签页数据
   */
  loadCurrentTabData() {
    switch (this.data.activeTab) {
      case 'assets':
        this.loadAssetList()
        break
      case 'records':
        this.loadRecordList()
        break
      case 'differences':
        this.loadDifferenceData()
        break
    }
  },

  /**
   * 刷新当前标签页
   */
  refreshCurrentTab() {
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      assetList: [],
      recordList: [],
      differenceList: [],
      finished: false
    })
    
    this.loadCurrentTabData()
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.refreshCurrentTab()
    
    setTimeout(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished || this.data.activeTab !== 'assets') {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadAssetList()
  },

  /**
   * 加载资产列表
   */
  async loadAssetList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        taskId: this.data.taskId,
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        params.keyword = this.data.searchKeyword.trim()
      }

      // 调用真实API - 通过盘点记录获取任务资产
      const response = await StocktakingAPI.getTaskAssets(this.data.taskId, params)
      
      if (response && response.code === 200) {
        // 适配后端返回格式
        const records = response.rows || response.data?.records || []
        const total = response.total || response.data?.total || 0

        // 更新列表数据
        let newList = []
        if (isRefresh || this.data.pageInfo.current === 1) {
          newList = records
        } else {
          newList = [...this.data.assetList, ...records]
        }

        // 计算分页信息
        const pageSize = this.data.pageInfo.size
        const currentPage = this.data.pageInfo.current
        const totalPages = Math.ceil(total / pageSize)

        this.setData({
          assetList: newList,
          'pageInfo.total': total,
          'pageInfo.size': pageSize,
          'pageInfo.current': currentPage,
          'pageInfo.pages': totalPages,
          finished: (currentPage >= totalPages) || (records.length === 0)
        })
      } else {
        throw new Error(response?.msg || '获取资产列表失败')
      }
    } catch (error) {
      console.error('❌ 加载资产列表失败:', error)
      Toast.fail('获取资产列表失败')
    } finally {
      this.setData({ loading: false })
    }
  },

,

  /**
   * 加载盘点记录列表
   */
  async loadRecordList() {
    try {
      // 调用真实API
      const response = await StocktakingAPI.getRecordList({ taskId: this.data.taskId })

      if (response && response.code === 200) {
        // 适配后端返回格式
        const recordList = response.rows || response.data || []
        this.setData({
          recordList: recordList
        })
      } else {
        throw new Error(response?.msg || '获取盘点记录失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点记录失败:', error)
      Toast.fail('获取盘点记录失败')
    }
  },



  /**
   * 加载差异数据
   */
  async loadDifferenceData() {
    try {
      // 调用真实API - 暂时使用空数据，等待后端提供差异统计接口
      const response = { code: 200, data: { stats: {}, list: [] } }

      if (response && response.code === 200 && response.data) {
        this.setData({
          differenceStats: response.data.stats,
          differenceList: response.data.list
        })
      } else {
        throw new Error(response?.msg || '获取差异数据失败')
      }
    } catch (error) {
      console.error('❌ 加载差异数据失败:', error)
      Toast.fail('获取差异数据失败')
    }
  },



  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })

    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行资产搜索，关键词:', keyword)

    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      assetList: [],
      finished: false
    })

    // 重新加载数据
    this.loadAssetList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshCurrentTab()
  },

  /**
   * 资产项点击
   */
  onAssetItemClick(event) {
    const asset = event.currentTarget.dataset.asset
    console.log('点击资产项:', asset)

    // 跳转到资产确认页面
    const url = asset.recordId
      ? `/pages/material/stocktaking/confirm?taskId=${this.data.taskId}&recordId=${asset.recordId}`
      : `/pages/material/stocktaking/confirm?taskId=${this.data.taskId}&assetId=${asset.assetId}`

    wx.navigateTo({
      url: url,
      fail: () => {
        Toast.fail('资产确认功能开发中')
      }
    })
  },

  /**
   * 开始任务
   */
  onStartTask() {
    console.log('开始任务:', this.data.taskInfo)

    // 跳转到扫码盘点页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/scan?taskId=${this.data.taskId}`,
      fail: () => {
        Toast.fail('扫码盘点功能开发中')
      }
    })
  },

  /**
   * 继续任务
   */
  onContinueTask() {
    console.log('继续任务:', this.data.taskInfo)

    // 跳转到扫码盘点页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/scan?taskId=${this.data.taskId}`,
      fail: () => {
        Toast.fail('扫码盘点功能开发中')
      }
    })
  },

  /**
   * 暂停任务
   */
  onPauseTask() {
    console.log('暂停任务:', this.data.taskInfo)

    wx.showModal({
      title: '暂停任务',
      content: '确定要暂停当前盘点任务吗？',
      success: (res) => {
        if (res.confirm) {
          // 模拟暂停任务
          Toast.success('任务已暂停')

          // 更新任务状态
          this.setData({
            'taskInfo.status': 'paused'
          })
        }
      }
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
