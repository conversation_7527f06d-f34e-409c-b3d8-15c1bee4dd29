// pages/maintenance/task/execute.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import MaintenanceAPI from '../../../utils/maintenance-api.js'

Page({
  data: {
    // 任务ID
    taskId: '',
    
    // 任务详情数据
    taskDetail: null,
    
    // 表单数据
    formData: {
      checkResult: '',
      resultDescription: '',
      problemDescription: '',
      solution: '',
      partList: []
    },
    
    // 步骤导航
    currentStep: 0,
    steps: [
      { text: '基本信息', icon: 'records' },
      { text: '维护记录', icon: 'edit' },
      { text: '备件使用', icon: 'setting-o' },
      { text: '拍照记录', icon: 'photo-o' }
    ],
    
    // 检查结果选项
    checkResultOptions: [
      { text: '正常', value: '正常' },
      { text: '异常', value: '异常' },
      { text: '需要维修', value: '需要维修' }
    ],
    
    // 照片记录
    photoList: [],
    
    // 加载状态
    loading: true,
    submitting: false,
    
    // 表单验证
    errors: {}
  },

  onLoad(options) {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取任务ID
    const { taskId } = options
    if (!taskId) {
      console.error('任务ID为空')
      Toast.fail('任务ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('任务ID:', taskId)
    this.setData({ taskId })

    // 加载任务详情
    this.loadTaskDetail()
  },

  /**
   * 加载任务详情
   */
  async loadTaskDetail() {
    try {
      this.setData({ loading: true })

      console.log('加载任务详情:', this.data.taskId)

      // 调用API获取任务详情
      let taskDetail = null
      try {
        const response = await MaintenanceAPI.getTaskDetail(this.data.taskId)
        taskDetail = MaintenanceAPI.handleApiResponse(response)

        // 格式化任务状态和优先级
        const statusInfo = MaintenanceAPI.formatTaskStatus(taskDetail.status)
        const priorityInfo = MaintenanceAPI.formatTaskPriority(taskDetail.priority)

        taskDetail = {
          ...taskDetail,
          statusName: statusInfo.name,
          statusType: statusInfo.type,
          statusColor: statusInfo.color,
          priorityName: priorityInfo.name,
          priorityType: priorityInfo.type,
          priorityColor: priorityInfo.color,
          overdue: MaintenanceAPI.isTaskOverdue(taskDetail.scheduledTime, taskDetail.status)
        }
      } catch (error) {
        console.warn('API调用失败，使用模拟数据:', error)
        // API调用失败时使用模拟数据
        taskDetail = this.generateMockTaskDetail(this.data.taskId)
      }

      // 初始化表单数据
      const formData = {
        checkResult: taskDetail.checkResult || '',
        resultDescription: taskDetail.resultDescription || '',
        problemDescription: taskDetail.problemDescription || '',
        solution: taskDetail.solution || '',
        partList: taskDetail.partList.map(part => ({
          ...part,
          actualQuantity: part.actualQuantity || 0,
          useStatus: part.useStatus || 1
        }))
      }

      this.setData({
        taskDetail: taskDetail,
        formData: formData
      })

      console.log('任务详情加载成功')

    } catch (error) {
      console.error('加载任务详情失败:', error)
      Toast.fail('加载任务详情失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 切换步骤
   */
  onStepChange(event) {
    const step = event.currentTarget.dataset.step
    
    // 验证当前步骤
    if (step > this.data.currentStep) {
      const isValid = this.validateCurrentStep()
      if (!isValid) {
        return
      }
    }
    
    this.setData({ currentStep: step })
  },

  /**
   * 下一步
   */
  onNextStep() {
    // 验证当前步骤
    const isValid = this.validateCurrentStep()
    if (!isValid) {
      return
    }
    
    const nextStep = this.data.currentStep + 1
    if (nextStep < this.data.steps.length) {
      this.setData({ currentStep: nextStep })
    }
  },

  /**
   * 上一步
   */
  onPrevStep() {
    const prevStep = this.data.currentStep - 1
    if (prevStep >= 0) {
      this.setData({ currentStep: prevStep })
    }
  },

  /**
   * 验证当前步骤
   */
  validateCurrentStep() {
    const { currentStep, formData } = this.data
    const errors = {}

    // 第二步：维护记录验证
    if (currentStep === 1) {
      if (!formData.checkResult) {
        errors.checkResult = '请选择检查结果'
      }
      if (!formData.resultDescription) {
        errors.resultDescription = '请填写结果描述'
      }

      // 如果选择了"异常"或"需要维修"，必须填写问题描述
      if ((formData.checkResult === '异常' || formData.checkResult === '需要维修') && !formData.problemDescription) {
        errors.problemDescription = '请描述发现的问题'
      }

      // 如果填写了问题描述，应该提供解决方案
      if (formData.problemDescription && !formData.solution) {
        errors.solution = '请提供解决方案'
      }
    }

    // 第三步：备件使用验证
    if (currentStep === 2) {
      // 验证备件使用数量
      formData.partList.forEach((part, index) => {
        if (part.useStatus === 2 && (!part.actualQuantity || part.actualQuantity <= 0)) {
          errors[`partList[${index}].actualQuantity`] = '已使用的备件必须填写使用数量'
        }

        // 验证使用数量不能超过库存
        if (part.useStatus === 2 && part.actualQuantity > part.currentStock) {
          errors[`partList[${index}].actualQuantity`] = `使用数量不能超过当前库存(${part.currentStock}${part.unit})`
        }
      })
    }

    this.setData({ errors })
    return Object.keys(errors).length === 0
  },

  /**
   * 表单输入变化
   */
  onInputChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: ''
    })
  },

  /**
   * 检查结果选择
   */
  onCheckResultChange(event) {
    this.setData({
      'formData.checkResult': event.detail,
      'errors.checkResult': ''
    })
  },

  /**
   * 备件使用状态变化
   */
  onPartUseStatusChange(event) {
    const { index } = event.currentTarget.dataset
    const useStatus = event.detail
    
    this.setData({
      [`formData.partList[${index}].useStatus`]: useStatus
    })
    
    // 如果设置为未使用，清空使用数量
    if (useStatus === 3) {
      this.setData({
        [`formData.partList[${index}].actualQuantity`]: 0
      })
    }
  },

  /**
   * 备件使用数量变化
   */
  onPartQuantityChange(event) {
    const { index } = event.currentTarget.dataset
    const value = event.detail
    
    this.setData({
      [`formData.partList[${index}].actualQuantity`]: value,
      [`errors.partList[${index}].actualQuantity`]: ''
    })
  },

  /**
   * 拍照
   */
  onTakePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0]
        
        // 添加到照片列表
        const photoList = [...this.data.photoList]
        photoList.push({
          id: Date.now().toString(),
          path: tempFilePath,
          description: ''
        })
        
        this.setData({ photoList })
      },
      fail: (error) => {
        console.error('拍照失败:', error)
        if (error.errMsg !== 'chooseImage:fail cancel') {
          Toast.fail('拍照失败')
        }
      }
    })
  },

  /**
   * 从相册选择
   */
  onChooseFromAlbum() {
    wx.chooseImage({
      count: 9,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        // 获取图片临时路径列表
        const tempFilePaths = res.tempFilePaths
        
        // 添加到照片列表
        const photoList = [...this.data.photoList]
        tempFilePaths.forEach(path => {
          photoList.push({
            id: Date.now().toString() + Math.random().toString(36).substring(2, 7),
            path: path,
            description: ''
          })
        })
        
        this.setData({ photoList })
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        if (error.errMsg !== 'chooseImage:fail cancel') {
          Toast.fail('选择图片失败')
        }
      }
    })
  },

  /**
   * 删除照片
   */
  onDeletePhoto(event) {
    const { index } = event.currentTarget.dataset
    
    Dialog.confirm({
      title: '删除照片',
      message: '确定要删除这张照片吗？',
      confirmButtonText: '删除',
      confirmButtonColor: '#ee0a24'
    }).then(() => {
      const photoList = [...this.data.photoList]
      photoList.splice(index, 1)
      this.setData({ photoList })
    }).catch(() => {
      // 取消删除
    })
  },

  /**
   * 照片描述变化
   */
  onPhotoDescriptionChange(event) {
    const { index } = event.currentTarget.dataset
    const value = event.detail
    
    this.setData({
      [`photoList[${index}].description`]: value
    })
  },

  /**
   * 预览照片
   */
  onPreviewPhoto(event) {
    const { index } = event.currentTarget.dataset
    const { photoList } = this.data
    
    wx.previewImage({
      current: photoList[index].path,
      urls: photoList.map(photo => photo.path)
    })
  },

  /**
   * 保存草稿
   */
  async onSaveDraft() {
    Toast.loading({
      message: '正在保存草稿...',
      forbidClick: true,
      duration: 0
    })

    try {
      // 构建提交数据
      const taskDto = {
        taskId: this.data.taskId,
        ...this.data.formData
      }

      // 调用API保存草稿
      await MaintenanceAPI.saveDraft(taskDto)

      Toast.clear()
      Toast.success('草稿保存成功')

      // 更新任务状态
      const statusInfo = MaintenanceAPI.formatTaskStatus(3)
      this.setData({
        'taskDetail.status': 3,
        'taskDetail.statusName': statusInfo.name,
        'taskDetail.statusType': statusInfo.type,
        'taskDetail.statusColor': statusInfo.color
      })
    } catch (error) {
      console.error('保存草稿失败:', error)
      Toast.clear()
      Toast.fail('保存草稿失败')
    }
  },

  /**
   * 提交任务
   */
  onSubmitTask() {
    // 验证所有步骤
    let allValid = true
    for (let step = 0; step < this.data.steps.length; step++) {
      this.setData({ currentStep: step })
      const isValid = this.validateCurrentStep()
      if (!isValid) {
        allValid = false
        console.log(`第${step + 1}步验证失败`)
        break
      }
    }

    if (!allValid) {
      Toast.fail('请完善必填信息')
      return
    }

    // 重置到最后一步
    this.setData({ currentStep: this.data.steps.length - 1 })

    Dialog.confirm({
      title: '提交任务',
      message: '确定要提交维护任务结果吗？提交后将无法修改。',
      confirmButtonText: '确定提交'
    }).then(() => {
      this.submitTaskResult()
    }).catch(() => {
      // 取消提交
      console.log('取消提交任务')
    })
  },

  /**
   * 提交任务结果
   */
  async submitTaskResult() {
    try {
      this.setData({ submitting: true })

      Toast.loading({
        message: '正在提交...',
        forbidClick: true,
        duration: 0
      })

      // 构建提交数据
      const taskDto = {
        taskId: this.data.taskId,
        ...this.data.formData
      }

      console.log('提交数据:', taskDto)

      // 先上传照片（如果有）
      if (this.data.photoList.length > 0) {
        try {
          // 实际项目中，这里需要先将本地照片上传到服务器，获取URL后再提交
          // 这里简化处理，假设已经上传成功
          console.log('上传照片:', this.data.photoList.length, '张')
          await MaintenanceAPI.uploadTaskPhotos(this.data.taskId, this.data.photoList)
        } catch (photoError) {
          console.error('上传照片失败:', photoError)
          // 照片上传失败不阻止任务提交
        }
      }

      // 调用API提交任务
      await MaintenanceAPI.submitTask(taskDto)

      Toast.clear()
      Toast.success('提交成功')

      // 延迟返回
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)

    } catch (error) {
      console.error('提交任务结果失败:', error)
      Toast.clear()
      Toast.fail('提交失败，请重试')
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 生成模拟任务详情数据
   */
  generateMockTaskDetail(taskId) {
    // 根据任务ID返回不同的模拟数据
    const mockDetails = {
      'MT20250401001': {
        taskId: 'MT20250401001',
        planId: 'MP20250301001',
        planName: '空调系统季度维护计划',
        taskTitle: '空调系统季度维护',
        assetId: 'AS20230001',
        assetName: '中央空调主机',
        assetCode: 'KT-2023-001',
        assetLocation: '设备楼3层机房',
        maintenanceItems: '1. 检查制冷系统压力\n2. 清洗过滤器\n3. 检查电气控制系统\n4. 测试运行状态',
        scheduledTime: '2025-04-15 09:00:00',
        actualStartTime: this.formatCurrentTime(),
        actualEndTime: null,
        responsibleType: 1,
        responsibleTypeName: '个人',
        responsibleId: 103,
        responsibleName: '张工',
        executorId: 103,
        executorName: '张工',
        status: 2,
        statusName: '执行中',
        priority: 3,
        priorityName: '高',
        checkResult: '',
        resultDescription: '',
        problemDescription: '',
        solution: '',
        overdue: false,
        partList: [
          {
            recordId: 'TP20250401001',
            partId: 'PT20240001',
            partName: '空调过滤网',
            specModel: 'KT-FW-001',
            unit: '个',
            plannedQuantity: 2,
            actualQuantity: 0,
            currentStock: 10,
            useStatus: 1,
            useStatusName: '计划使用'
          },
          {
            recordId: 'TP20250401002',
            partId: 'PT20240002',
            partName: '制冷剂',
            specModel: 'R410A',
            unit: 'kg',
            plannedQuantity: 1.5,
            actualQuantity: 0,
            currentStock: 5,
            useStatus: 1,
            useStatusName: '计划使用'
          }
        ]
      },
      'MT20250401002': {
        taskId: 'MT20250401002',
        planId: 'MP20250302001',
        planName: '电梯月度安全检查计划',
        taskTitle: '电梯月度安全检查',
        assetId: 'AS20220001',
        assetName: '1号电梯',
        assetCode: 'DT-2022-001',
        assetLocation: '主楼电梯间',
        maintenanceItems: '1. 检查电梯运行声音\n2. 检查电梯门开关\n3. 检查紧急制动系统\n4. 检查电梯轿厢内设施',
        scheduledTime: '2025-04-10 14:00:00',
        actualStartTime: '2025-04-10 14:05:00',
        actualEndTime: null,
        responsibleType: 1,
        responsibleTypeName: '个人',
        responsibleId: 104,
        responsibleName: '李工',
        executorId: 104,
        executorName: '李工',
        status: 2,
        statusName: '执行中',
        priority: 4,
        priorityName: '紧急',
        checkResult: '异常',
        resultDescription: '电梯运行正常，但门关闭时有异响',
        problemDescription: '发现电梯门关闭时有异响',
        solution: '需要调整门机构',
        overdue: true,
        partList: [
          {
            recordId: 'TP20250402001',
            partId: 'PT20240003',
            partName: '电梯门传感器',
            specModel: 'DT-CG-002',
            unit: '个',
            plannedQuantity: 1,
            actualQuantity: 0,
            currentStock: 3,
            useStatus: 1,
            useStatusName: '计划使用'
          }
        ]
      }
    }

    // 如果找不到对应ID的数据，返回默认数据
    return mockDetails[taskId] || mockDetails['MT20250401001']
  },

  /**
   * 格式化当前时间
   */
  formatCurrentTime() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hour = String(now.getHours()).padStart(2, '0')
    const minute = String(now.getMinutes()).padStart(2, '0')
    const second = String(now.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`
  }
})
