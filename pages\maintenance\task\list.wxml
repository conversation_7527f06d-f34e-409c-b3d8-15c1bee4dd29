<!--pages/maintenance/task/list.wxml-->
<view class="task-list-page">
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索任务标题"
      use-action-slot
      bind:search="onSearchTask"
      bind:clear="onClearSearch"
      bind:change="onSearchTask"
      shape="round"
      background="#ffffff"
    >
      <view slot="action" class="action-buttons">
        <view bind:tap="showFilterPopup" class="filter-btn">
          <van-icon name="filter-o" size="18" />
        </view>
      </view>
    </van-search>
  </view>

  <!-- 状态筛选标签 -->
  <view class="filter-tags" wx:if="{{ statusFilter.length > 0 }}">
    <view class="tag-list">
      <view class="tag-item" wx:for="{{ statusFilter }}" wx:key="*this">
        <van-tag
          type="primary"
          closeable
          size="medium"
          data-status="{{ item }}"
          bind:close="onRemoveStatusFilter"
        >
          {{ statusOptions[item - 1].label }}
        </van-tag>
      </view>
      <view class="tag-clear" bind:tap="onClearStatusFilter" wx:if="{{ statusFilter.length > 0 }}">
        <text>清除筛选</text>
      </view>
    </view>
  </view>

  <!-- 任务列表 -->
  <view class="task-list-container">
    <view class="task-list" wx:if="{{ taskList.length > 0 }}">
      <view 
        class="task-item {{ item.overdue ? 'task-item--overdue' : '' }}"
        wx:for="{{ taskList }}"
        wx:key="taskId"
        bind:tap="onTaskItemClick"
        data-task="{{ item }}"
      >
        <view class="task-card">
          <!-- 任务标题和状态 -->
          <view class="task-header">
            <view class="task-title">{{ item.taskTitle }}</view>
            <view class="task-status">
              <van-tag
                type="{{ item.statusType || (item.status === 1 ? 'warning' : item.status === 2 ? 'primary' : item.status === 7 ? 'success' : 'default') }}"
                size="medium"
              >
                {{ item.statusName }}
              </van-tag>
            </view>
          </view>

          <!-- 资产信息 -->
          <view class="task-asset">
            <view class="asset-name">
              <van-icon name="cluster-o" size="14" />
              <text>{{ item.assetName }}</text>
            </view>
            <view class="asset-code">
              <text>{{ item.assetCode }}</text>
            </view>
          </view>

          <!-- 任务信息 -->
          <view class="task-info">
            <view class="task-time">
              <van-icon name="clock-o" size="14" />
              <text>{{ item.scheduledTime }}</text>
              <view class="overdue-tag" wx:if="{{ item.overdue }}">
                <van-tag type="danger" size="mini">逾期</van-tag>
              </view>
            </view>
            <view class="task-priority">
              <van-tag
                plain
                type="{{ item.priorityType || (item.priority === 4 ? 'danger' : item.priority === 3 ? 'warning' : item.priority === 2 ? 'primary' : 'default') }}"
                size="mini"
              >
                {{ item.priorityName }}
              </van-tag>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{ loading && !refreshing }}">
      <van-loading type="spinner" size="24px">加载中...</van-loading>
    </view>

    <!-- 无数据状态 -->
    <view class="empty-container" wx:if="{{ isEmpty && !loading }}">
      <van-empty image="search" description="暂无维护任务">
        <view class="empty-tips">当前没有符合条件的维护任务</view>
      </van-empty>
    </view>
  </view>

  <!-- 筛选弹窗 -->
  <van-popup
    show="{{ showFilterPopup }}"
    position="bottom"
    round
    custom-style="height: 50%;"
    bind:close="closeFilterPopup"
  >
    <view class="filter-popup">
      <view class="filter-header">
        <view class="filter-title">筛选条件</view>
        <view class="filter-close" bind:tap="closeFilterPopup">
          <van-icon name="cross" size="18" />
        </view>
      </view>

      <view class="filter-content">
        <view class="filter-section">
          <view class="section-title">任务状态</view>
          <van-checkbox-group value="{{ statusFilter }}" bind:change="onStatusFilterChange">
            <view class="checkbox-list">
              <view class="checkbox-item" wx:for="{{ statusOptions }}" wx:key="value">
                <van-checkbox 
                  name="{{ item.value }}" 
                  shape="square"
                  checked-color="{{ item.color }}"
                >
                  {{ item.label }}
                </van-checkbox>
              </view>
            </view>
          </van-checkbox-group>
        </view>
      </view>

      <view class="filter-footer">
        <van-button type="default" block bind:click="closeFilterPopup">确定</van-button>
      </view>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
