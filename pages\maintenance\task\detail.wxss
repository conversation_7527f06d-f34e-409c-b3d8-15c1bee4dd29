/* pages/maintenance/task/detail.wxss */
.task-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* 详情区域通用样式 */
.detail-section {
  margin: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.section-action {
  display: flex;
  align-items: center;
  color: #1989fa;
  font-size: 24rpx;
}

.action-text {
  margin-right: 4rpx;
}

/* 基本信息区域 */
.task-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 28rpx;
  color: #323233;
  word-break: break-all;
}

.priority-value {
  display: flex;
  align-items: center;
}

.overdue-time {
  color: #ee0a24;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

/* 资产信息区域 */
.asset-info {
  padding: 16rpx 0;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.asset-code {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 8rpx;
}

.asset-location {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #969799;
}

.asset-location van-icon {
  margin-right: 4rpx;
}

/* 维护事项区域 */
.maintenance-items {
  padding: 16rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
}

.items-content {
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* 备品备件区域 */
.parts-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.part-item {
  padding: 16rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
}

.part-info {
  margin-bottom: 8rpx;
}

.part-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
}

.part-spec {
  font-size: 24rpx;
  color: #646566;
  margin-top: 4rpx;
}

.part-quantity, .part-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;
}

.quantity-label, .stock-label {
  font-size: 24rpx;
  color: #969799;
}

.quantity-value, .stock-value {
  font-size: 24rpx;
  color: #323233;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stock-warning {
  color: #ee0a24;
}

/* 执行记录区域 */
.execution-record {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.record-item {
  padding: 16rpx;
  background-color: #f7f8fa;
  border-radius: 8rpx;
}

.record-label {
  font-size: 24rpx;
  color: #969799;
  margin-bottom: 8rpx;
}

.record-value {
  font-size: 28rpx;
  color: #323233;
  line-height: 1.6;
  word-break: break-all;
}

.empty-record {
  padding: 32rpx 0;
}

.empty-tip {
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}

/* 操作按钮区域 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 24rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 空状态 */
.empty-container {
  padding: 64rpx 0;
}

.empty-tips {
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}
