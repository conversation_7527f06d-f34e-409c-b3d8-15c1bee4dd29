<!--pages/material/stocktaking/confirm.wxml-->
<view class="page-container">
  
  <!-- 资产基本信息 -->
  <view class="asset-info-section">
    <view class="section-title">
      <van-icon name="info-o" size="20" color="#1989fa" />
      <text class="title-text">资产基本信息</text>
    </view>
    
    <van-cell-group>
      <van-cell title="资产名称" value="{{ assetInfo.assetName }}" />
      <van-cell title="资产编码" value="{{ assetInfo.assetCode }}" />
      <van-cell title="规格型号" value="{{ assetInfo.specModel || '无' }}" />
      <van-cell title="资产分类" value="{{ assetInfo.categoryName || '无' }}" />
      <van-cell title="购置日期" value="{{ assetInfo.purchaseDate || '无' }}" />
      <van-cell title="使用部门" value="{{ assetInfo.departmentName || '无' }}" />
    </van-cell-group>
  </view>

  <!-- 账面信息 vs 实际信息 -->
  <view class="comparison-section">
    <view class="section-title">
      <van-icon name="balance-list-o" size="20" color="#ff976a" />
      <text class="title-text">账面信息 vs 实际信息</text>
    </view>

    <!-- 存放位置对比 -->
    <view class="comparison-item">
      <view class="comparison-header">
        <text class="comparison-title">存放位置</text>
      </view>
      <view class="comparison-content">
        <view class="comparison-row">
          <view class="comparison-label">账面位置：</view>
          <view class="comparison-value book">{{ assetInfo.bookLocation }}</view>
        </view>
        <view class="comparison-row">
          <view class="comparison-label">实际位置：</view>
          <view class="comparison-value actual">
            <van-field
              value="{{ confirmData.actualLocation }}"
              placeholder="请输入实际位置"
              bind:change="onActualLocationChange"
              clearable
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 资产状态对比 -->
    <view class="comparison-item">
      <view class="comparison-header">
        <text class="comparison-title">资产状态</text>
      </view>
      <view class="comparison-content">
        <view class="comparison-row">
          <view class="comparison-label">账面状态：</view>
          <view class="comparison-value book">{{ assetInfo.bookStatus }}</view>
        </view>
        <view class="comparison-row">
          <view class="comparison-label">实际状态：</view>
          <view class="comparison-value actual">
            <text 
              class="status-selector" 
              bind:tap="onSelectStatus"
            >
              {{ confirmData.actualStatus || '请选择实际状态' }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 发现状态 -->
  <view class="found-section">
    <view class="section-title">
      <van-icon name="eye-o" size="20" color="#07c160" />
      <text class="title-text">发现状态</text>
    </view>
    
    <van-radio-group 
      value="{{ confirmData.foundStatus }}" 
      bind:change="onFoundStatusChange"
      direction="horizontal"
    >
      <van-radio name="found" custom-class="found-radio">找到</van-radio>
      <van-radio name="not_found" custom-class="found-radio">未找到</van-radio>
    </van-radio-group>
  </view>

  <!-- 差异处理 -->
  <view wx:if="{{ hasDifference }}" class="difference-section">
    <view class="section-title">
      <van-icon name="warning-o" size="20" color="#ee0a24" />
      <text class="title-text">差异处理</text>
    </view>
    
    <van-cell-group>
      <van-cell 
        title="差异类型" 
        value="{{ differenceType }}" 
        is-link
        bind:click="onSelectDifferenceType"
      />
      <van-cell title="差异原因">
        <van-field
          value="{{ confirmData.differenceReason }}"
          placeholder="请输入差异原因"
          bind:change="onDifferenceReasonChange"
          type="textarea"
          autosize
          maxlength="200"
          show-word-limit
        />
      </van-cell>
    </van-cell-group>
  </view>

  <!-- 现场记录 -->
  <view class="record-section">
    <view class="section-title">
      <van-icon name="photo-o" size="20" color="#7232dd" />
      <text class="title-text">现场记录</text>
    </view>
    
    <!-- 拍照记录 -->
    <view class="photo-record">
      <view class="record-title">拍照记录</view>
      <view class="photo-grid">
        <block wx:for="{{ confirmData.photos }}" wx:key="index">
          <view class="photo-item">
            <van-image
              src="{{ item }}"
              width="120rpx"
              height="120rpx"
              fit="cover"
              radius="8rpx"
              bind:click="onPreviewPhoto"
              data-index="{{ index }}"
            />
            <view 
              class="photo-delete"
              bind:tap="onDeletePhoto"
              data-index="{{ index }}"
            >
              <van-icon name="cross" size="12" color="#fff" />
            </view>
          </view>
        </block>
        
        <!-- 添加照片按钮 -->
        <view 
          wx:if="{{ confirmData.photos.length < 3 }}"
          class="photo-add"
          bind:tap="onTakePhoto"
        >
          <van-icon name="plus" size="32" color="#c8c9cc" />
          <text class="add-text">拍照</text>
        </view>
      </view>
    </view>

    <!-- 语音备注 -->
    <view class="voice-record">
      <view class="record-title">语音备注</view>
      <view class="voice-actions">
        <van-button 
          type="default" 
          size="small" 
          icon="volume-o"
          bind:click="onRecordVoice"
        >
          录制语音
        </van-button>
        
        <van-button 
          wx:if="{{ confirmData.voiceUrl }}"
          type="primary" 
          size="small" 
          icon="play-circle-o"
          bind:click="onPlayVoice"
        >
          播放语音
        </van-button>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="text-record">
      <view class="record-title">备注信息</view>
      <van-field
        value="{{ confirmData.remark }}"
        placeholder="请输入备注信息（可选）"
        bind:change="onRemarkChange"
        type="textarea"
        autosize
        maxlength="500"
        show-word-limit
      />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <van-button 
      type="default" 
      size="large" 
      bind:click="onCancel"
      custom-class="cancel-btn"
    >
      取消
    </van-button>
    
    <van-button 
      type="primary" 
      size="large" 
      bind:click="onConfirm"
      custom-class="confirm-btn"
    >
      确认提交
    </van-button>
  </view>

  <!-- 资产状态选择 -->
  <van-action-sheet
    show="{{ showStatusPicker }}"
    actions="{{ statusActions }}"
    bind:close="onCloseStatusPicker"
    bind:select="onSelectStatusAction"
    title="请选择实际状态"
  />

  <!-- 差异类型选择 -->
  <van-action-sheet
    show="{{ showDifferenceTypePicker }}"
    actions="{{ differenceTypeActions }}"
    bind:close="onCloseDifferenceTypePicker"
    bind:select="onSelectDifferenceTypeAction"
    title="请选择差异类型"
  />

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
