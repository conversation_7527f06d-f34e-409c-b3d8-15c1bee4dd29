// pages/asset/search/search.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import AssetAPI from '../../../utils/asset-api.js'

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    searchHistory: [],
    
    // 搜索结果
    searchResults: [],
    totalCount: 0,
    hasMore: false,
    
    // 页面状态
    showSuggestions: false,
    showResults: false,
    searching: false,
    loadingMore: false,
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10
    },
    
    // 搜索防抖定时器
    searchTimer: null
  },

  onLoad(options) {
    console.log('🔍 资产搜索页面加载，参数:', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
    
    // 处理传入的搜索关键词
    if (options.keyword) {
      const keyword = decodeURIComponent(options.keyword)
      this.setData({ searchKeyword: keyword })
      this.performSearch(keyword)
    }
  },

  onShow() {
    // 页面显示时刷新搜索历史
    this.loadSearchHistory()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 加载搜索历史
    this.loadSearchHistory()
    
    console.log('✅ 资产搜索页面初始化完成')
  },

  /**
   * 加载搜索历史
   */
  loadSearchHistory() {
    const history = AssetAPI.getSearchHistory()
    this.setData({ searchHistory: history })
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({ searchKeyword: keyword })
    
    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }
    
    // 如果有关键词，设置防抖搜索
    if (keyword.trim()) {
      const timer = setTimeout(() => {
        this.performSearch(keyword)
      }, 500)
      
      this.setData({ searchTimer: timer })
    } else {
      // 清空关键词时显示建议
      this.setData({
        showResults: false,
        showSuggestions: true,
        searchResults: []
      })
    }
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 搜索框获得焦点
   */
  onSearchFocus() {
    if (!this.data.searchKeyword) {
      this.setData({ showSuggestions: true })
    }
  },

  /**
   * 搜索框失去焦点
   */
  onSearchBlur() {
    // 延迟隐藏建议，避免点击建议项时立即隐藏
    setTimeout(() => {
      if (!this.data.searchKeyword) {
        this.setData({ showSuggestions: false })
      }
    }, 200)
  },

  /**
   * 执行搜索
   */
  async performSearch(keyword, isLoadMore = false) {
    if (!keyword || !keyword.trim()) {
      return
    }

    const trimmedKeyword = keyword.trim()
    
    // 如果不是加载更多，重置分页
    if (!isLoadMore) {
      this.setData({
        'pageInfo.current': 1,
        searching: true,
        showResults: true,
        showSuggestions: false
      })
    } else {
      this.setData({ loadingMore: true })
    }

    try {
      console.log('📤 执行搜索，关键词:', trimmedKeyword)

      // 调用搜索API
      const response = await AssetAPI.searchAssets(trimmedKeyword, {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      })

      console.log('📥 搜索结果响应:', response)

      // 使用新的API响应处理方法
      const paginationData = AssetAPI.handlePaginationResponse(response)

      // 格式化搜索结果
      const formattedResults = AssetAPI.formatAssetList(paginationData.records)

      // 更新搜索结果
      const newResults = isLoadMore
        ? [...this.data.searchResults, ...formattedResults]
        : formattedResults

      // 判断是否还有更多数据
      const hasMore = newResults.length < paginationData.total &&
                     paginationData.records.length === this.data.pageInfo.size

      this.setData({
        searchResults: newResults,
        totalCount: paginationData.total,
        hasMore: hasMore
      })

      // 保存搜索历史（仅首次搜索时）
      if (!isLoadMore) {
        AssetAPI.saveSearchHistory(trimmedKeyword)
        this.loadSearchHistory()
      }

      console.log(`✅ 搜索完成，找到 ${newResults.length}/${paginationData.total} 个结果`)
    } catch (error) {
      console.error('❌ 搜索失败:', error)
      Toast.fail('搜索失败，请重试')
    } finally {
      this.setData({ 
        searching: false,
        loadingMore: false
      })
    }
  },

  /**
   * 清除搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: '',
      showResults: false,
      showSuggestions: true,
      searchResults: []
    })
  },

  /**
   * 扫码搜索
   */
  onScanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        const scanContent = res.result
        
        // 设置搜索关键词并搜索
        this.setData({ searchKeyword: scanContent })
        this.performSearch(scanContent)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        Toast.fail('扫码失败')
      }
    })
  },

  /**
   * 历史记录点击
   */
  onHistoryClick(event) {
    const keyword = event.currentTarget.dataset.keyword
    this.setData({ searchKeyword: keyword })
    this.performSearch(keyword)
  },

  /**
   * 删除历史记录
   */
  onHistoryRemove(event) {
    const { keyword, index } = event.currentTarget.dataset
    AssetAPI.removeSearchHistory(keyword)
    this.loadSearchHistory()
    
    // 阻止事件冒泡，避免触发点击事件
    event.stopPropagation()
  },

  /**
   * 清除搜索历史
   */
  clearSearchHistory() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          AssetAPI.clearSearchHistory()
          this.loadSearchHistory()
          Toast.success('搜索历史已清除')
        }
      }
    })
  },

  /**
   * 快速搜索
   */
  quickSearch(event) {
    const { type, value } = event.currentTarget.dataset

    let searchParams = {}

    switch (type) {
      case 'status':
        searchParams.status = value
        break
      case 'date':
        if (value === 'today') {
          const today = new Date().toISOString().split('T')[0]
          searchParams.purchaseDate = today
        }
        break
      case 'price':
        if (value === 'high') {
          searchParams.minPrice = 10000 // 假设高价值资产标准
        }
        break
    }

    // 构建URL参数
    const urlParams = Object.keys(searchParams)
      .map(key => `${key}=${encodeURIComponent(searchParams[key])}`)
      .join('&')

    // 跳转到资产列表页面并传递筛选参数
    wx.navigateTo({
      url: `/pages/asset/list/list?${urlParams}`,
      fail: () => {
        Toast.fail('功能开发中')
      }
    })
  },

  /**
   * 资产项点击
   */
  onAssetItemClick(event) {
    const asset = event.currentTarget.dataset.asset
    console.log('点击资产项:', asset)

    // 跳转到资产详情页面
    wx.navigateTo({
      url: `/pages/asset/detail/detail?assetId=${asset.assetId}`,
      fail: () => {
        Toast.fail('资产详情功能开发中')
      }
    })
  },

  /**
   * 查看全部结果
   */
  viewAllResults() {
    const keyword = encodeURIComponent(this.data.searchKeyword)
    wx.navigateTo({
      url: `/pages/asset/list/list?keyword=${keyword}`,
      fail: () => {
        Toast.fail('功能开发中')
      }
    })
  },

  /**
   * 加载更多结果
   */
  loadMoreResults() {
    if (this.data.loadingMore || !this.data.hasMore) {
      return
    }
    
    // 增加页码
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    // 执行搜索
    this.performSearch(this.data.searchKeyword, true)
  },

  /**
   * 跳转到资产列表
   */
  goToAssetList() {
    wx.navigateTo({
      url: '/pages/asset/list/list',
      fail: () => {
        Toast.fail('功能开发中')
      }
    })
  }
})
