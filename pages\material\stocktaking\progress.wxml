<!--pages/material/stocktaking/progress.wxml-->
<view class="page-container">
  
  <!-- 总体进度 -->
  <view class="overall-progress">
    <view class="progress-header">
      <text class="progress-title">{{ taskInfo.taskName }}</text>
      <text class="progress-subtitle">任务编号：{{ taskInfo.taskCode }}</text>
    </view>
    
    <view class="progress-stats">
      <view class="stat-item">
        <text class="stat-value">{{ taskInfo.totalCount }}</text>
        <text class="stat-label">总资产数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ taskInfo.completedCount }}</text>
        <text class="stat-label">已盘点</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ taskInfo.remainingCount }}</text>
        <text class="stat-label">待盘点</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ taskInfo.differenceCount }}</text>
        <text class="stat-label">有差异</text>
      </view>
    </view>
    
    <view class="progress-bar">
      <view class="progress-info">
        <text class="progress-text">完成进度</text>
        <text class="progress-percent">{{ taskInfo.progressPercent }}%</text>
      </view>
      <van-progress 
        percentage="{{ taskInfo.progressPercent }}" 
        stroke-width="12" 
        color="#07c160"
        track-color="#f2f3f5"
      />
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-section">
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
      <van-tab title="个人进度" name="personal"></van-tab>
      <van-tab title="团队进度" name="team"></van-tab>
      <van-tab title="分类统计" name="category"></van-tab>
    </van-tabs>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    
    <!-- 个人进度 -->
    <view wx:if="{{ activeTab === 'personal' }}" class="personal-tab">
      <scroll-view class="scroll-container" scroll-y>
        
        <!-- 今日统计 -->
        <view class="today-stats">
          <view class="section-title">
            <van-icon name="calendar-o" size="20" color="#1989fa" />
            <text class="title-text">今日统计</text>
          </view>
          
          <van-cell-group>
            <van-cell title="今日盘点数量" value="{{ personalStats.todayCount }}" />
            <van-cell title="今日发现差异" value="{{ personalStats.todayDifference }}" />
            <van-cell title="平均用时" value="{{ personalStats.avgTime }}" />
            <van-cell title="效率排名" value="{{ personalStats.ranking }}" />
          </van-cell-group>
        </view>

        <!-- 历史记录 -->
        <view class="history-records">
          <view class="section-title">
            <van-icon name="records" size="20" color="#ff976a" />
            <text class="title-text">最近盘点记录</text>
          </view>
          
          <view class="record-list">
            <block wx:if="{{ personalRecords.length > 0 }}">
              <view 
                wx:for="{{ personalRecords }}" 
                wx:key="recordId"
                class="record-item"
              >
                <view class="record-content">
                  <view class="record-header">
                    <text class="asset-name">{{ item.assetName }}</text>
                    <van-tag 
                      type="{{ item.hasDifference ? 'danger' : 'success' }}" 
                      size="small"
                    >
                      {{ item.hasDifference ? '有差异' : '正常' }}
                    </van-tag>
                  </view>
                  <view class="record-info">
                    <text class="asset-code">{{ item.assetCode }}</text>
                    <text class="record-time">{{ item.recordTime }}</text>
                  </view>
                </view>
              </view>
            </block>

            <!-- 空状态 -->
            <van-empty 
              wx:if="{{ personalRecords.length === 0 }}"
              image="search" 
              description="暂无盘点记录"
            />
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 团队进度 -->
    <view wx:if="{{ activeTab === 'team' }}" class="team-tab">
      <scroll-view class="scroll-container" scroll-y>
        
        <!-- 团队排行榜 -->
        <view class="team-ranking">
          <view class="section-title">
            <van-icon name="medal-o" size="20" color="#ff976a" />
            <text class="title-text">团队排行榜</text>
          </view>
          
          <view class="ranking-list">
            <block wx:if="{{ teamRanking.length > 0 }}">
              <view 
                wx:for="{{ teamRanking }}" 
                wx:key="userId"
                class="ranking-item"
              >
                <view class="ranking-content">
                  <view class="ranking-left">
                    <view class="ranking-number {{ index < 3 ? 'top-three' : '' }}">
                      {{ index + 1 }}
                    </view>
                    <view class="user-info">
                      <text class="user-name">{{ item.userName }}</text>
                      <text class="user-dept">{{ item.departmentName }}</text>
                    </view>
                  </view>
                  <view class="ranking-right">
                    <text class="count-text">{{ item.completedCount }}</text>
                    <text class="count-label">已盘点</text>
                  </view>
                </view>
              </view>
            </block>

            <!-- 空状态 -->
            <van-empty 
              wx:if="{{ teamRanking.length === 0 }}"
              image="search" 
              description="暂无团队数据"
            />
          </view>
        </view>

        <!-- 部门统计 -->
        <view class="dept-stats">
          <view class="section-title">
            <van-icon name="cluster-o" size="20" color="#7232dd" />
            <text class="title-text">部门统计</text>
          </view>
          
          <view class="dept-list">
            <block wx:if="{{ deptStats.length > 0 }}">
              <view 
                wx:for="{{ deptStats }}" 
                wx:key="deptId"
                class="dept-item"
              >
                <view class="dept-content">
                  <view class="dept-header">
                    <text class="dept-name">{{ item.departmentName }}</text>
                    <text class="dept-progress">{{ item.progressPercent }}%</text>
                  </view>
                  <view class="dept-progress-bar">
                    <van-progress 
                      percentage="{{ item.progressPercent }}" 
                      stroke-width="6" 
                      color="#1989fa"
                      track-color="#f2f3f5"
                    />
                  </view>
                  <view class="dept-info">
                    <text class="dept-detail">{{ item.completedCount }}/{{ item.totalCount }}</text>
                    <text class="dept-people">{{ item.peopleCount }}人参与</text>
                  </view>
                </view>
              </view>
            </block>

            <!-- 空状态 -->
            <van-empty 
              wx:if="{{ deptStats.length === 0 }}"
              image="search" 
              description="暂无部门数据"
            />
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 分类统计 -->
    <view wx:if="{{ activeTab === 'category' }}" class="category-tab">
      <scroll-view class="scroll-container" scroll-y>
        
        <!-- 资产分类统计 -->
        <view class="category-stats">
          <view class="section-title">
            <van-icon name="apps-o" size="20" color="#07c160" />
            <text class="title-text">资产分类统计</text>
          </view>
          
          <view class="category-list">
            <block wx:if="{{ categoryStats.length > 0 }}">
              <view 
                wx:for="{{ categoryStats }}" 
                wx:key="categoryId"
                class="category-item"
              >
                <view class="category-content">
                  <view class="category-header">
                    <text class="category-name">{{ item.categoryName }}</text>
                    <text class="category-progress">{{ item.progressPercent }}%</text>
                  </view>
                  <view class="category-progress-bar">
                    <van-progress 
                      percentage="{{ item.progressPercent }}" 
                      stroke-width="6" 
                      color="{{ item.progressPercent === 100 ? '#07c160' : '#1989fa' }}"
                      track-color="#f2f3f5"
                    />
                  </view>
                  <view class="category-info">
                    <view class="info-row">
                      <text class="info-label">总数量：</text>
                      <text class="info-value">{{ item.totalCount }}</text>
                    </view>
                    <view class="info-row">
                      <text class="info-label">已盘点：</text>
                      <text class="info-value">{{ item.completedCount }}</text>
                    </view>
                    <view class="info-row">
                      <text class="info-label">有差异：</text>
                      <text class="info-value difference">{{ item.differenceCount }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </block>

            <!-- 空状态 -->
            <van-empty 
              wx:if="{{ categoryStats.length === 0 }}"
              image="search" 
              description="暂无分类数据"
            />
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
