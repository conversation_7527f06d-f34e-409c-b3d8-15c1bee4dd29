/* pages/maintenance/asset/scan.wxss */
.scan-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24rpx;
}

/* 扫码区域 */
.scan-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 48rpx 24rpx;
  text-align: center;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.scan-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.scan-subtitle {
  font-size: 28rpx;
  color: #646566;
  margin-bottom: 48rpx;
}

.scan-button-container {
  max-width: 400rpx;
  margin: 0 auto;
}

/* 资产信息区域 */
.asset-info-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
  padding: 0 8rpx;
}

.asset-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
}

.asset-action {
  display: flex;
  align-items: center;
  color: #1989fa;
  font-size: 24rpx;
}

.action-text {
  margin-right: 4rpx;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 28rpx;
  color: #646566;
}

.detail-value {
  font-size: 28rpx;
  color: #323233;
  font-weight: 500;
}

/* 任务列表区域 */
.task-list-section {
  margin-bottom: 24rpx;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.task-item:active {
  opacity: 0.8;
}

.task-item--overdue {
  border: 2rpx solid #ee0a24;
}

.task-card {
  padding: 24rpx;
}

/* 任务标题和状态 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.task-status {
  flex-shrink: 0;
}

/* 任务信息 */
.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #969799;
}

.task-time van-icon {
  margin-right: 8rpx;
}

.overdue-tag {
  margin-left: 8rpx;
}

.task-priority {
  flex-shrink: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 64rpx 0;
}

/* 空状态 */
.empty-tasks,
.error-container,
.initial-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 64rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.empty-tip {
  display: block;
  font-size: 24rpx;
  color: #969799;
  text-align: center;
  margin-top: 16rpx;
}
