/* pages/material/stocktaking/task/detail.wxss */
.page-container {
  height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 任务信息区域 */
.task-info-section {
  background-color: #fff;
  margin-bottom: 16rpx;
}

.task-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.task-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.task-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.task-code {
  font-size: 26rpx;
  color: #969799;
}

/* 进度区域 */
.progress-section {
  padding: 24rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.progress-text {
  font-size: 26rpx;
  color: #646566;
}

/* 操作按钮区域 */
.action-section {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.pause-btn {
  margin-top: 16rpx !important;
}

/* 标签页区域 */
.tabs-section {
  background-color: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

/* 标签页内容 */
.tab-content {
  flex: 1;
  overflow: hidden;
}

.assets-tab,
.records-tab,
.differences-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  padding: 16rpx;
  border-bottom: 1rpx solid #ebedf0;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  height: 100%;
}

/* 资产列表 */
.asset-list {
  padding: 16rpx;
}

.asset-item {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.asset-content {
  padding: 24rpx;
}

.asset-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.asset-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-row {
  display: flex;
  align-items: center;
}

.label {
  font-size: 26rpx;
  color: #969799;
  min-width: 160rpx;
}

.value {
  font-size: 26rpx;
  color: #646566;
  flex: 1;
}

.value.difference {
  color: #ee0a24;
}

.value.normal {
  color: #07c160;
}

/* 盘点记录列表 */
.record-list {
  padding: 16rpx;
}

.record-item {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.record-content {
  padding: 24rpx;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.record-time {
  font-size: 24rpx;
  color: #969799;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 差异汇总 */
.difference-summary {
  padding: 16rpx;
}

.difference-list {
  margin-top: 24rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  padding: 0 16rpx 16rpx;
}

.difference-item {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.difference-content {
  padding: 24rpx;
}

.diff-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.diff-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}
