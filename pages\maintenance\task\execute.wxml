<!--pages/maintenance/task/execute.wxml-->
<view class="task-execute-page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading type="spinner" size="36px">加载中...</van-loading>
  </view>

  <!-- 任务执行内容 -->
  <block wx:elif="{{ taskDetail }}">
    <!-- 步骤导航 -->
    <view class="steps-container">
      <van-steps
        steps="{{ steps }}"
        active="{{ currentStep }}"
        active-color="#1989fa"
      />
    </view>

    <!-- 步骤内容区域 -->
    <view class="step-content">
      <!-- 步骤1：基本信息 -->
      <view class="step-panel" wx:if="{{ currentStep === 0 }}">
        <view class="panel-title">任务基本信息</view>
        
        <van-cell-group>
          <van-cell title="任务标题" value="{{ taskDetail.taskTitle }}" />
          <van-cell title="任务编号" value="{{ taskDetail.taskId }}" />
          <van-cell title="资产名称" value="{{ taskDetail.assetName }}" />
          <van-cell title="资产编码" value="{{ taskDetail.assetCode }}" />
          <van-cell title="资产位置" value="{{ taskDetail.assetLocation }}" />
          <van-cell title="计划时间" value="{{ taskDetail.formattedScheduledTime || taskDetail.scheduledTime }}" />
          <van-cell title="开始时间" value="{{ taskDetail.formattedActualStartTime || taskDetail.actualStartTime }}" />
          <van-cell title="负责人" value="{{ taskDetail.responsibleName }}" />
          <van-cell title="执行人" value="{{ taskDetail.executorName }}" />
        </van-cell-group>

        <view class="maintenance-items">
          <view class="items-title">维护事项</view>
          <view class="items-content">{{ taskDetail.maintenanceItems }}</view>
        </view>
      </view>

      <!-- 步骤2：维护记录 -->
      <view class="step-panel" wx:if="{{ currentStep === 1 }}">
        <view class="panel-title">维护记录</view>
        
        <van-cell-group>
          <!-- 检查结果 -->
          <van-cell title="检查结果" required>
            <van-radio-group
              value="{{ formData.checkResult }}"
              bind:change="onCheckResultChange"
              direction="horizontal"
            >
              <van-radio
                wx:for="{{ checkResultOptions }}"
                wx:key="value"
                name="{{ item.value }}"
                checked-color="#1989fa"
              >{{ item.text }}</van-radio>
            </van-radio-group>
          </van-cell>
          <view class="error-message" wx:if="{{ errors.checkResult }}">{{ errors.checkResult }}</view>
          
          <!-- 结果描述 -->
          <van-field
            value="{{ formData.resultDescription }}"
            label="结果描述"
            type="textarea"
            placeholder="请描述维护结果"
            autosize="{{ {minHeight: 80} }}"
            required
            border="{{ true }}"
            bind:change="onInputChange"
            data-field="resultDescription"
            error-message="{{ errors.resultDescription }}"
          />
          
          <!-- 问题描述 -->
          <van-field
            value="{{ formData.problemDescription }}"
            label="问题描述"
            type="textarea"
            placeholder="请描述发现的问题（如有）"
            autosize="{{ {minHeight: 80} }}"
            border="{{ true }}"
            bind:change="onInputChange"
            data-field="problemDescription"
            error-message="{{ errors.problemDescription }}"
          />

          <!-- 解决方案 -->
          <van-field
            value="{{ formData.solution }}"
            label="解决方案"
            type="textarea"
            placeholder="请描述解决方案（如有）"
            autosize="{{ {minHeight: 80} }}"
            border="{{ true }}"
            bind:change="onInputChange"
            data-field="solution"
            error-message="{{ errors.solution }}"
          />
        </van-cell-group>
      </view>

      <!-- 步骤3：备件使用 -->
      <view class="step-panel" wx:if="{{ currentStep === 2 }}">
        <view class="panel-title">备件使用</view>
        
        <view class="parts-list" wx:if="{{ formData.partList.length > 0 }}">
          <view class="part-item" wx:for="{{ formData.partList }}" wx:key="recordId">
            <view class="part-header">
              <view class="part-name">{{ item.partName }}</view>
              <view class="part-spec">{{ item.specModel }}</view>
            </view>
            
            <view class="part-info">
              <view class="info-row">
                <view class="info-label">计划用量:</view>
                <view class="info-value">{{ item.plannedQuantity }} {{ item.unit }}</view>
              </view>
              <view class="info-row">
                <view class="info-label">当前库存:</view>
                <view class="info-value {{ item.currentStock < item.plannedQuantity ? 'stock-warning' : '' }}">
                  {{ item.currentStock }} {{ item.unit }}
                  <van-tag wx:if="{{ item.currentStock < item.plannedQuantity }}" type="danger" size="mini">库存不足</van-tag>
                </view>
              </view>
            </view>
            
            <view class="part-usage">
              <view class="usage-title">使用状态:</view>
              <van-radio-group
                value="{{ item.useStatus }}"
                bind:change="onPartUseStatusChange"
                data-index="{{ index }}"
                direction="horizontal"
              >
                <van-radio name="2" checked-color="#07c160">已使用</van-radio>
                <van-radio name="3" checked-color="#969799">未使用</van-radio>
              </van-radio-group>
            </view>
            
            <view class="part-quantity" wx:if="{{ item.useStatus === 2 }}">
              <view class="quantity-title">使用数量:</view>
              <van-stepper
                value="{{ item.actualQuantity }}"
                min="0"
                max="{{ item.currentStock }}"
                step="0.1"
                decimal-length="1"
                input-width="80px"
                button-size="32px"
                bind:change="onPartQuantityChange"
                data-index="{{ index }}"
              />
              <view class="error-message" wx:if="{{ errors['partList['+index+'].actualQuantity'] }}">
                {{ errors['partList['+index+'].actualQuantity'] }}
              </view>
            </view>
          </view>
        </view>
        
        <view class="empty-parts" wx:else>
          <van-empty image="search" description="暂无备品备件">
            <text class="empty-tip">该任务未关联备品备件</text>
          </van-empty>
        </view>
      </view>

      <!-- 步骤4：拍照记录 -->
      <view class="step-panel" wx:if="{{ currentStep === 3 }}">
        <view class="panel-title">拍照记录</view>
        
        <view class="photo-actions">
          <van-button type="primary" icon="photograph" bind:click="onTakePhoto">拍照</van-button>
          <van-button type="info" icon="photo-o" bind:click="onChooseFromAlbum">从相册选择</van-button>
        </view>
        
        <view class="photo-list" wx:if="{{ photoList.length > 0 }}">
          <view class="photo-item" wx:for="{{ photoList }}" wx:key="id">
            <view class="photo-image" bind:tap="onPreviewPhoto" data-index="{{ index }}">
              <van-image
                width="100%"
                height="200rpx"
                fit="cover"
                src="{{ item.path }}"
                radius="8rpx"
              />
              <view class="photo-delete" catch:tap="onDeletePhoto" data-index="{{ index }}">
                <van-icon name="cross" size="20" />
              </view>
            </view>
            
            <van-field
              value="{{ item.description }}"
              placeholder="添加照片描述"
              border="{{ false }}"
              bind:change="onPhotoDescriptionChange"
              data-index="{{ index }}"
            />
          </view>
        </view>
        
        <view class="empty-photos" wx:else>
          <van-empty image="photo-o" description="暂无照片">
            <text class="empty-tip">请拍照或从相册选择照片</text>
          </van-empty>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-bar">
      <view class="step-buttons">
        <van-button
          wx:if="{{ currentStep > 0 }}"
          type="default"
          size="large"
          bind:click="onPrevStep"
        >上一步</van-button>

        <van-button
          wx:if="{{ currentStep < steps.length - 1 }}"
          type="primary"
          size="large"
          bind:click="onNextStep"
        >下一步</van-button>

        <block wx:if="{{ currentStep === steps.length - 1 }}">
          <van-button
            type="default"
            size="large"
            bind:click="onSaveDraft"
          >保存草稿</van-button>
          <van-button
            type="primary"
            size="large"
            bind:click="onSubmitTask"
            loading="{{ submitting }}"
            disabled="{{ submitting }}"
          >提交任务</van-button>
        </block>
      </view>
    </view>
  </block>

  <!-- 无数据状态 -->
  <view class="empty-container" wx:else>
    <van-empty image="error" description="未找到任务详情">
      <view class="empty-tips">该任务可能已被删除或您没有查看权限</view>
    </van-empty>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
