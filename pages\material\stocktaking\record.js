// pages/material/stocktaking/record.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { getStocktakingDetails, recordStocktaking } from '../../../api/material.js'

Page({
  data: {
    // 盘点单ID
    stocktakingId: '',

    // 盘点明细列表
    detailList: [],

    // 搜索条件
    searchKeyword: '',

    // 分页信息
    pageInfo: {
      current: 1,
      size: 20,
      total: 0,
      pages: 0
    },

    // 加载状态
    loading: true,
    finished: false,
    refreshing: false,

    // 当前录入的明细
    currentDetail: null,

    // 显示录入弹窗
    showRecordDialog: false,

    // 显示手动输入弹窗
    showManualDialog: false,

    // 显示差异原因选择
    showReasonPicker: false,

    // 录入数据
    recordData: {
      actualQuantity: '',
      differenceReason: '',
      photos: []
    },

    // 手动输入编码
    manualCode: '',

    // 差异原因选项
    reasonActions: [
      { name: '无差异', value: '' },
      { name: '自然损耗', value: '自然损耗' },
      { name: '盘盈', value: '盘盈' },
      { name: '盘亏', value: '盘亏' },
      { name: '损坏报废', value: '损坏报废' },
      { name: '位置错误', value: '位置错误' },
      { name: '其他原因', value: '其他原因' }
    ]
  },

  onLoad(options) {
    console.log('📋 盘点记录页面加载', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      console.log('❌ 页面权限检查失败')
      this.setData({ loading: false })
      return
    }

    // 获取盘点单ID
    const { stocktakingId } = options
    if (!stocktakingId) {
      console.log('❌ 盘点单ID为空')
      Toast.fail('盘点单ID不能为空')
      this.setData({ loading: false })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('✅ 盘点单ID:', stocktakingId)
    this.setData({ stocktakingId })

    // 加载盘点明细
    this.loadStocktakingDetails()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData()
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }

    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })

    this.loadStocktakingDetails()
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      'pageInfo.current': 1,
      detailList: [],
      finished: false,
      refreshing: true
    })

    this.loadStocktakingDetails().finally(() => {
      this.setData({ refreshing: false })
    })
  },

  /**
   * 加载盘点明细
   */
  async loadStocktakingDetails(isRefresh = false) {
    if (this.data.loading) {
      console.log('⏳ 正在加载中，跳过重复请求')
      return
    }

    console.log('🚀 开始加载盘点明细，盘点单ID:', this.data.stocktakingId)
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 根据关键词特征判断搜索类型
        if (/^[A-Z0-9]+$/i.test(keyword)) {
          // 如果是字母数字组合，按物品编码搜索
          params.itemCode = keyword
        } else {
          // 否则按物品名称搜索
          params.itemName = keyword
        }
      }

      console.log('📤 发送请求参数:', params)
      const response = await getStocktakingDetails(this.data.stocktakingId, params)
      console.log('📥 接收到响应:', response)

      if (response && response.code === 200 && response.data) {
        console.log('✅ 成功获取盘点明细:', response.data)

        let newData = []

        // 处理不同的数据格式
        if (Array.isArray(response.data.records)) {
          newData = response.data.records
          console.log('📊 使用分页格式数据，记录数:', newData.length)
        } else if (Array.isArray(response.data)) {
          newData = response.data
          console.log('📊 使用数组格式数据，记录数:', newData.length)
        } else {
          console.log('⚠️ 数据格式异常，使用空数组')
          newData = []
        }

        // 处理分页数据
        let detailList = []
        if (this.data.pageInfo.current === 1) {
          detailList = newData
        } else {
          detailList = [...this.data.detailList, ...newData]
        }

        // 更新分页信息
        const total = response.data.total || newData.length
        const pages = response.data.pages || Math.ceil(total / this.data.pageInfo.size)
        const finished = this.data.pageInfo.current >= pages || newData.length < this.data.pageInfo.size

        console.log('📈 分页信息:', { total, pages, finished, currentPage: this.data.pageInfo.current })

        this.setData({
          detailList: detailList,
          'pageInfo.total': total,
          'pageInfo.pages': pages,
          finished: finished
        })

        console.log('✅ 盘点明细加载完成，共', detailList.length, '条记录')
      } else {
        console.error('❌ 响应格式错误:', response)
        throw new Error(response?.msg || '获取盘点明细失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点明细失败:', error)

      // 如果是首次加载失败，显示错误信息
      if (this.data.pageInfo.current === 1) {
        Toast.fail('获取明细失败: ' + (error.message || '未知错误'))

        // 临时添加测试数据，便于调试界面
        console.log('🧪 使用测试数据')
        const testData = [
          {
            detailId: 'test001',
            itemId: 'item001',
            itemName: '测试物品A',
            itemCode: 'TEST001',
            specModel: '规格A',
            bookQuantity: 100,
            actualQuantity: null,
            differenceQuantity: null,
            differenceReason: '',
            unit: '个',
            shelfLocation: 'A-01-01',
            status: 0,
            photos: []
          },
          {
            detailId: 'test002',
            itemId: 'item002',
            itemName: '测试物品B',
            itemCode: 'TEST002',
            specModel: '规格B',
            bookQuantity: 50,
            actualQuantity: 48,
            differenceQuantity: -2,
            differenceReason: '自然损耗',
            unit: '个',
            shelfLocation: 'A-01-02',
            status: 2,
            photos: []
          }
        ]

        this.setData({
          detailList: testData,
          'pageInfo.total': testData.length,
          'pageInfo.pages': 1,
          finished: true
        })
      }

      // 恢复页码
      if (this.data.pageInfo.current > 1) {
        this.setData({
          'pageInfo.current': this.data.pageInfo.current - 1
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })

    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行盘点明细搜索，关键词:', keyword)

    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      detailList: [],
      finished: false
    })

    // 重新加载数据
    this.loadStocktakingDetails(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 扫码盘点
   */
  onScanCode() {
    console.log('📷 开始扫码盘点')

    wx.scanCode({
      scanType: ['barCode', 'qrCode'],
      success: (res) => {
        console.log('扫码结果:', res.result)
        this.searchItemByCode(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        if (error.errMsg && error.errMsg.includes('cancel')) {
          // 用户取消扫码，不显示错误提示
          return
        }
        Toast.fail('扫码失败，请重试')
      }
    })
  },

  /**
   * 手动输入
   */
  onManualInput() {
    console.log('⌨️ 手动输入物品编码')
    this.setData({
      showManualDialog: true,
      manualCode: ''
    })
  },

  /**
   * 手动编码输入
   */
  onManualCodeChange(event) {
    this.setData({
      manualCode: event.detail
    })
  },

  /**
   * 根据编码搜索
   */
  onSearchByCode() {
    const code = this.data.manualCode.trim()
    if (!code) {
      Toast.fail('请输入物品编码')
      return
    }

    this.setData({ showManualDialog: false })
    this.searchItemByCode(code)
  },

  /**
   * 取消手动输入
   */
  onCancelManual() {
    this.setData({
      showManualDialog: false,
      manualCode: ''
    })
  },

  /**
   * 根据编码搜索物品
   */
  searchItemByCode(code) {
    console.log('🔍 根据编码搜索物品:', code)

    // 在明细列表中查找匹配的物品
    const foundItem = this.data.detailList.find(item =>
      item.itemCode === code || item.itemCode.toLowerCase() === code.toLowerCase()
    )

    if (foundItem) {
      // 找到物品，直接打开录入弹窗
      this.onRecordItem({ currentTarget: { dataset: { detail: foundItem } } })
    } else {
      // 未找到物品，设置搜索关键词并搜索
      this.setData({ searchKeyword: code })
      this.performSearch(code)
      Toast('未找到匹配物品，已为您搜索相关结果')
    }
  },

  /**
   * 明细项点击
   */
  onDetailItemClick(event) {
    const detail = event.currentTarget.dataset.detail
    console.log('点击明细项:', detail)

    // 直接打开录入弹窗
    this.onRecordItem(event)
  },

  /**
   * 录入盘点结果
   */
  onRecordItem(event) {
    const detail = event.currentTarget.dataset.detail
    console.log('录入盘点结果:', detail)

    this.setData({
      currentDetail: detail,
      'recordData.actualQuantity': detail.actualQuantity !== null ? detail.actualQuantity.toString() : '',
      'recordData.differenceReason': detail.differenceReason || '',
      'recordData.photos': detail.photos || [],
      showRecordDialog: true
    })
  },

  /**
   * 录入数据变化
   */
  onRecordDataChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail

    this.setData({
      [`recordData.${field}`]: value
    })
  },

  /**
   * 显示差异原因选择
   */
  onShowReasonPicker() {
    this.setData({ showReasonPicker: true })
  },

  /**
   * 关闭差异原因选择
   */
  onCloseReasonPicker() {
    this.setData({ showReasonPicker: false })
  },

  /**
   * 选择差异原因
   */
  onSelectReason(event) {
    const reason = event.detail.value
    this.setData({
      'recordData.differenceReason': reason,
      showReasonPicker: false
    })
  },

  /**
   * 拍照
   */
  onTakePhoto() {
    console.log('📷 拍照记录')

    wx.chooseMedia({
      count: 3 - this.data.recordData.photos.length,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功:', res)
        this.uploadPhotos(res.tempFiles)
      },
      fail: (error) => {
        console.error('选择图片失败:', error)
        if (error.errMsg && !error.errMsg.includes('cancel')) {
          Toast.fail('选择图片失败')
        }
      }
    })
  },

  /**
   * 上传照片
   */
  async uploadPhotos(tempFiles) {
    Toast.loading('上传中...')

    try {
      const uploadPromises = tempFiles.map(file => this.uploadSinglePhoto(file.tempFilePath))
      const uploadResults = await Promise.all(uploadPromises)

      const newPhotos = [...this.data.recordData.photos, ...uploadResults]
      this.setData({
        'recordData.photos': newPhotos
      })

      Toast.clear()
      Toast.success('上传成功')
    } catch (error) {
      console.error('上传照片失败:', error)
      Toast.clear()
      Toast.fail('上传失败')
    }
  },

  /**
   * 上传单张照片
   */
  uploadSinglePhoto(filePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${wx.getStorageSync('baseUrl') || ''}/common/upload`,
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token') || ''}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data.url || data.data)
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('解析响应失败'))
          }
        },
        fail: reject
      })
    })
  },

  /**
   * 预览照片
   */
  onPreviewPhoto(event) {
    const index = event.currentTarget.dataset.index
    const photos = this.data.recordData.photos

    wx.previewImage({
      current: photos[index],
      urls: photos
    })
  },

  /**
   * 删除照片
   */
  onDeletePhoto(event) {
    const index = event.currentTarget.dataset.index
    const photos = [...this.data.recordData.photos]
    photos.splice(index, 1)

    this.setData({
      'recordData.photos': photos
    })
  },

  /**
   * 确认录入
   */
  async onConfirmRecord() {
    const { currentDetail, recordData } = this.data

    // 验证实盘数量
    if (!recordData.actualQuantity || recordData.actualQuantity === '') {
      Toast.fail('请输入实盘数量')
      return
    }

    const actualQuantity = parseFloat(recordData.actualQuantity)
    if (isNaN(actualQuantity) || actualQuantity < 0) {
      Toast.fail('实盘数量必须为非负数')
      return
    }

    try {
      // 构建录入数据
      const submitData = {
        detailId: currentDetail.detailId,
        actualQuantity: actualQuantity,
        differenceReason: recordData.differenceReason.trim()
      }

      // 如果有照片，添加照片信息
      if (recordData.photos && recordData.photos.length > 0) {
        submitData.photos = recordData.photos
      }

      console.log('💾 提交盘点记录:', submitData)

      // 调用API录入盘点结果
      const response = await recordStocktaking(submitData)

      if (response && response.code === 200) {
        Toast.success('录入成功')

        // 关闭弹窗
        this.setData({ showRecordDialog: false })

        // 刷新明细列表
        this.refreshData()
      } else {
        throw new Error(response?.msg || '录入失败')
      }
    } catch (error) {
      console.error('❌ 录入盘点结果失败:', error)
      Toast.fail(error.message || '录入失败')
    }
  },

  /**
   * 取消录入
   */
  onCancelRecord() {
    this.setData({
      showRecordDialog: false,
      currentDetail: null,
      'recordData.actualQuantity': '',
      'recordData.differenceReason': '',
      'recordData.photos': []
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})