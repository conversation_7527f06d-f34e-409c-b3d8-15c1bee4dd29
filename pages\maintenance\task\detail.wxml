<!--pages/maintenance/task/detail.wxml-->
<view class="task-detail-page">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading type="spinner" size="36px">加载中...</van-loading>
  </view>

  <!-- 任务详情内容 -->
  <block wx:elif="{{ taskDetail }}">
    <!-- 基本信息区域 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">基本信息</view>
        <view class="task-status">
          <van-tag
            type="{{ taskDetail.statusType || (taskDetail.status === 1 ? 'warning' : taskDetail.status === 2 ? 'primary' : taskDetail.status === 7 ? 'success' : taskDetail.status === 8 ? 'default' : 'default') }}"
            size="medium"
          >
            {{ taskDetail.statusName }}
          </van-tag>
        </view>
      </view>

      <view class="task-title">{{ taskDetail.taskTitle }}</view>

      <view class="info-grid">
        <view class="info-item">
          <view class="info-label">任务编号</view>
          <view class="info-value">{{ taskDetail.taskId }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">优先级</view>
          <view class="info-value priority-value">
            <van-tag
              plain
              type="{{ taskDetail.priorityType || (taskDetail.priority === 4 ? 'danger' : taskDetail.priority === 3 ? 'warning' : taskDetail.priority === 2 ? 'primary' : 'default') }}"
              size="mini"
            >
              {{ taskDetail.priorityName }}
            </van-tag>
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">计划时间</view>
          <view class="info-value {{ taskDetail.overdue ? 'overdue-time' : '' }}">
            {{ taskDetail.formattedScheduledTime || taskDetail.scheduledTime }}
            <van-tag wx:if="{{ taskDetail.overdue }}" type="danger" size="mini">逾期</van-tag>
          </view>
        </view>
        <view class="info-item" wx:if="{{ taskDetail.actualStartTime }}">
          <view class="info-label">开始时间</view>
          <view class="info-value">{{ taskDetail.formattedActualStartTime || taskDetail.actualStartTime }}</view>
        </view>
        <view class="info-item" wx:if="{{ taskDetail.actualEndTime }}">
          <view class="info-label">完成时间</view>
          <view class="info-value">{{ taskDetail.formattedActualEndTime || taskDetail.actualEndTime }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">负责人</view>
          <view class="info-value">{{ taskDetail.responsibleName }}</view>
        </view>
        <view class="info-item" wx:if="{{ taskDetail.executorName }}">
          <view class="info-label">执行人</view>
          <view class="info-value">{{ taskDetail.executorName }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">所属计划</view>
          <view class="info-value">{{ taskDetail.planName }}</view>
        </view>
      </view>
    </view>

    <!-- 资产信息区域 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">资产信息</view>
        <view class="section-action" bind:tap="onViewAsset">
          <text class="action-text">查看详情</text>
          <van-icon name="arrow" size="14" />
        </view>
      </view>

      <view class="asset-info">
        <view class="asset-name">{{ taskDetail.assetName }}</view>
        <view class="asset-code">{{ taskDetail.assetCode }}</view>
        <view class="asset-location">
          <van-icon name="location-o" size="14" />
          <text>{{ taskDetail.assetLocation }}</text>
        </view>
      </view>
    </view>

    <!-- 维护事项区域 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">维护事项</view>
      </view>

      <view class="maintenance-items">
        <text class="items-content">{{ taskDetail.maintenanceItems }}</text>
      </view>
    </view>

    <!-- 备品备件区域 -->
    <view class="detail-section" wx:if="{{ taskDetail.partList && taskDetail.partList.length > 0 }}">
      <view class="section-header">
        <view class="section-title">所需备品备件</view>
      </view>

      <view class="parts-list">
        <view class="part-item" wx:for="{{ taskDetail.partList }}" wx:key="recordId">
          <view class="part-info">
            <view class="part-name">{{ item.partName }}</view>
            <view class="part-spec">{{ item.specModel }}</view>
          </view>
          <view class="part-quantity">
            <view class="quantity-label">计划用量:</view>
            <view class="quantity-value">{{ item.plannedQuantity }} {{ item.unit }}</view>
          </view>
          <view class="part-stock">
            <view class="stock-label">当前库存:</view>
            <view class="stock-value {{ item.currentStock < item.plannedQuantity ? 'stock-warning' : '' }}">
              {{ item.currentStock }} {{ item.unit }}
              <van-tag wx:if="{{ item.currentStock < item.plannedQuantity }}" type="danger" size="mini">库存不足</van-tag>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 执行记录区域 -->
    <view class="detail-section" wx:if="{{ taskDetail.status >= 2 }}">
      <view class="section-header">
        <view class="section-title">执行记录</view>
      </view>

      <view class="execution-record">
        <view class="record-item" wx:if="{{ taskDetail.checkResult }}">
          <view class="record-label">检查结果</view>
          <view class="record-value">{{ taskDetail.checkResult }}</view>
        </view>
        <view class="record-item" wx:if="{{ taskDetail.resultDescription }}">
          <view class="record-label">结果描述</view>
          <view class="record-value">{{ taskDetail.resultDescription }}</view>
        </view>
        <view class="record-item" wx:if="{{ taskDetail.problemDescription }}">
          <view class="record-label">问题描述</view>
          <view class="record-value">{{ taskDetail.problemDescription }}</view>
        </view>
        <view class="record-item" wx:if="{{ taskDetail.solution }}">
          <view class="record-label">解决方案</view>
          <view class="record-value">{{ taskDetail.solution }}</view>
        </view>

        <!-- 无执行记录提示 -->
        <view class="empty-record" wx:if="{{ !taskDetail.checkResult && !taskDetail.resultDescription && !taskDetail.problemDescription && !taskDetail.solution }}">
          <van-empty image="search" description="暂无执行记录">
            <text class="empty-tip">任务正在执行中，尚未提交执行记录</text>
          </van-empty>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-bar">
      <view class="action-buttons">
        <block wx:if="{{ canStart }}">
          <van-button type="primary" block bind:click="onStartTask">开始执行</van-button>
        </block>
        <block wx:if="{{ taskDetail.status === 2 }}">
          <van-button type="primary" block bind:click="onContinueTask">继续执行</van-button>
        </block>
        <block wx:if="{{ canCancel }}">
          <van-button type="danger" block bind:click="onCancelTask">取消任务</van-button>
        </block>
      </view>
    </view>
  </block>

  <!-- 无数据状态 -->
  <view class="empty-container" wx:else>
    <van-empty image="error" description="未找到任务详情">
      <view class="empty-tips">该任务可能已被删除或您没有查看权限</view>
    </van-empty>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
