/* pages/material/stocktaking/task/list.wxss */
.page-container {
  height: 100vh;
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  padding: 16rpx;
  border-bottom: 1rpx solid #ebedf0;
}

/* 标签页区域 */
.tabs-section {
  background-color: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

/* 列表区域 */
.list-section {
  flex: 1;
  overflow: hidden;
}

.scroll-container {
  height: 100%;
}

.task-list {
  padding: 16rpx;
}

/* 任务项样式 */
.task-item-group {
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.task-item {
  padding: 0 !important;
}

.task-content {
  padding: 24rpx;
}

/* 任务头部 */
.task-header {
  margin-bottom: 16rpx;
}

.task-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.task-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
}

.task-code {
  font-size: 24rpx;
  color: #969799;
}

/* 任务信息 */
.task-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-text {
  font-size: 26rpx;
  color: #646566;
  margin-left: 8rpx;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 20rpx;
}

.progress-header {
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #323233;
  font-weight: 500;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.action-btn {
  min-width: 120rpx !important;
}

/* 空状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  color: #969799;
  font-size: 24rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
  color: #c8c9cc;
  font-size: 24rpx;
}
